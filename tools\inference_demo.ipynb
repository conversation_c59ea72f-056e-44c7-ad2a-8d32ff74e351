{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_YeUiqAoCaoV", "outputId": "06e4c803-ac46-49e6-b8fa-1a85c23fa482"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sys.platform: win32\n", "Python: 3.10.17 | packaged by conda-forge | (main, Apr 10 2025, 22:06:35) [MSC v.1943 64 bit (AMD64)]\n", "CUDA available: True\n", "MUSA available: False\n", "numpy_random_seed: 2147483648\n", "GPU 0: NVIDIA GeForce RTX 4090\n", "CUDA_HOME: None\n", "MSVC: n/a, reason: fileno\n", "PyTorch: 2.3.0+cu118\n", "PyTorch compiling details: PyTorch built with:\n", "  - C++ Version: 201703\n", "  - MSVC 192930151\n", "  - Intel(R) oneAPI Math Kernel Library Version 2021.4-Product Build 20210904 for Intel(R) 64 architecture applications\n", "  - Intel(R) MKL-DNN v3.3.6 (Git Hash 86e6af5974177e513fd3fee58425e1063e7f1361)\n", "  - OpenMP 2019\n", "  - LAPACK is enabled (usually provided by MKL)\n", "  - CPU capability usage: AVX2\n", "  - CUDA Runtime 11.8\n", "  - NVCC architecture flags: -gencode;arch=compute_37,code=sm_37;-gencode;arch=compute_50,code=sm_50;-gencode;arch=compute_60,code=sm_60;-gencode;arch=compute_61,code=sm_61;-gencode;arch=compute_70,code=sm_70;-gencode;arch=compute_75,code=sm_75;-gencode;arch=compute_80,code=sm_80;-gencode;arch=compute_86,code=sm_86;-gencode;arch=compute_90,code=sm_90;-gencode;arch=compute_37,code=compute_37\n", "  - CuDNN 8.7\n", "  - Magma 2.5.4\n", "  - Build settings: BLAS_INFO=mkl, BUILD_TYPE=Release, CUDA_VERSION=11.8, CUDNN_VERSION=8.7.0, CXX_COMPILER=C:/actions-runner/_work/pytorch/pytorch/builder/windows/tmp_bin/sccache-cl.exe, CXX_FLAGS=/DWIN32 /D_WINDOWS /GR /EHsc /Zc:__cplusplus /bigobj /FS /utf-8 -DUSE_PTHREADPOOL -DNDEBUG -DUSE_KINETO -DLIBKINETO_NOCUPTI -DLIBKINETO_NOROCTRACER -DUSE_FBGEMM -DUSE_XNNPACK -DSYMBOLICATE_MOBILE_DEBUG_HANDLE /wd4624 /wd4068 /wd4067 /wd4267 /wd4661 /wd4717 /wd4244 /wd4804 /wd4273, LAPACK_INFO=mkl, PERF_WITH_AVX=1, PERF_WITH_AVX2=1, PERF_WITH_AVX512=1, TORCH_VERSION=2.3.0, USE_CUDA=ON, USE_CUDNN=ON, USE_CUSPARSELT=OFF, USE_EXCEPTION_PTR=1, USE_GFLAGS=OFF, USE_GLOG=OFF, USE_GLOO=ON, USE_MKL=ON, USE_MKLDNN=ON, USE_MPI=OFF, USE_NCCL=OFF, USE_NNPACK=OFF, USE_OPENMP=ON, USE_ROCM=OFF, USE_ROCM_KERNEL_ASSERT=OFF, \n", "\n", "TorchVision: 0.18.0+cu118\n", "OpenCV: 4.11.0\n", "MMEngine: 0.10.7\n", "MMDetection: 3.3.0+6a241b0\n"]}], "source": ["import mmdet\n", "from mmengine.utils import get_git_hash\n", "from mmengine.utils.dl_utils import collect_env as collect_base_env\n", "\n", "\n", "def collect_env():\n", "    \"\"\"Collect the information of the running environments.\"\"\"\n", "    env_info = collect_base_env()\n", "    env_info[\"MMDetection\"] = f\"{mmdet.__version__}+{get_git_hash()[:7]}\"\n", "    return env_info\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    for name, val in collect_env().items():\n", "        print(f\"{name}: {val}\")"]}, {"cell_type": "markdown", "metadata": {"id": "fLgFRMtP91ue"}, "source": ["## `DetInferencer`\n", "\n", "### Basic Usage\n", "\n", "We use the high-level API `DetInferencer` implemented in the MMDetection. This API is created to ease the inference process. The details of the codes can be found [here](https://github.com/open-mmlab/mmdetection/blob/dev-3.x/mmdet/apis/det_inferencer.py)."]}, {"cell_type": "markdown", "metadata": {"id": "Atft9tjcwgeD"}, "source": ["- To load custom config and weight, you can pass the path to the config file to `model` and the path to the weight to `weights`."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eukDD4Rzwp9P", "outputId": "0a34392c-0544-4a90-c844-7628d184efc0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loads checkpoint by local backend from path: E:\\tobacco\\tobacco_det\\work_dirs\\config\\epoch_160.pth\n", "06/27 18:21:26 - mmengine - \u001b[5m\u001b[4m\u001b[33mWARNING\u001b[0m - Failed to search registry with scope \"mmdet\" in the \"function\" registry tree. As a workaround, the current \"function\" registry in \"mmengine\" is used to build instance. This may cause unexpected failure when running the built modules. Please check whether \"mmdet\" is a correct scope, or whether the registry is initialized.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "55a7ab43f6b74758985c0e44cb899314", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\openmmlab\\lib\\site-packages\\mmengine\\visualization\\visualizer.py:196: UserWarning: Failed to add <class 'mmengine.visualization.vis_backend.LocalVisBackend'>, please provide the `save_dir` argument.\n", "  warnings.warn(f'Failed to add {vis_backend.__class__}, '\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">c:\\Users\\<USER>\\miniconda3\\envs\\openmmlab\\lib\\site-packages\\torch\\functional.py:512: UserWarning: torch.meshgrid: \n", "in an upcoming release, it will be required to pass the indexing argument. (Triggered internally at \n", "..\\aten\\src\\ATen\\native\\TensorShape.cpp:3588.)\n", "  return _VF.meshgrid(tensors, **kwargs)  # type: ignore[attr-defined]\n", "</pre>\n"], "text/plain": ["c:\\Users\\<USER>\\miniconda3\\envs\\openmmlab\\lib\\site-packages\\torch\\functional.py:512: UserWarning: torch.meshgrid: \n", "in an upcoming release, it will be required to pass the indexing argument. (Triggered internally at \n", "..\\aten\\src\\ATen\\native\\TensorShape.cpp:3588.)\n", "  return _VF.meshgrid(tensors, **kwargs)  # type: ignore[attr-defined]\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">\n", "</pre>\n"], "text/plain": ["\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'predictions': [{'labels': [0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    0,\n", "    ...],\n", "   'scores': [0.6548541784286499,\n", "    0.6396106481552124,\n", "    0.6337077021598816,\n", "    0.6280182003974915,\n", "    0.6246059536933899,\n", "    0.6181548833847046,\n", "    0.6171674132347107,\n", "    0.616227924823761,\n", "    0.6160089373588562,\n", "    0.6158881783485413,\n", "    0.6155765652656555,\n", "    0.6154070496559143,\n", "    0.6136937141418457,\n", "    0.6126875877380371,\n", "    0.6122254133224487,\n", "    0.611725389957428,\n", "    0.6115058064460754,\n", "    0.6111008524894714,\n", "    0.6089572906494141,\n", "    0.6089476346969604,\n", "    0.6088487505912781,\n", "    0.6066907048225403,\n", "    0.606432318687439,\n", "    0.6050754189491272,\n", "    0.6044703722000122,\n", "    0.6043397188186646,\n", "    0.6037091612815857,\n", "    0.6020910739898682,\n", "    0.600732684135437,\n", "    0.5999658703804016,\n", "    0.5987628698348999,\n", "    0.5984249711036682,\n", "    0.5979788303375244,\n", "    0.5976378321647644,\n", "    0.5972521901130676,\n", "    0.596583366394043,\n", "    0.5962780117988586,\n", "    0.5952536463737488,\n", "    0.5947525501251221,\n", "    0.59458988904953,\n", "    0.5925298929214478,\n", "    0.592076301574707,\n", "    0.5910677909851074,\n", "    0.590873122215271,\n", "    0.5906368494033813,\n", "    0.5894711017608643,\n", "    0.5892083048820496,\n", "    0.5891202092170715,\n", "    0.5881880521774292,\n", "    0.5877268314361572,\n", "    0.5865779519081116,\n", "    0.5859376788139343,\n", "    0.5854327082633972,\n", "    0.5852404236793518,\n", "    0.5846831798553467,\n", "    0.5838547348976135,\n", "    0.5837969779968262,\n", "    0.5829693675041199,\n", "    0.5826931595802307,\n", "    0.5825802683830261,\n", "    0.5821971893310547,\n", "    0.582084596157074,\n", "    0.5816923379898071,\n", "    0.5814437866210938,\n", "    0.5812222957611084,\n", "    0.5810902118682861,\n", "    0.5803957581520081,\n", "    0.5801429748535156,\n", "    0.5800484418869019,\n", "    0.5799624919891357,\n", "    0.5796213746070862,\n", "    0.5793748497962952,\n", "    0.5793168544769287,\n", "    0.578413724899292,\n", "    0.5782164931297302,\n", "    0.5772155523300171,\n", "    0.5770146250724792,\n", "    0.576822817325592,\n", "    0.5764331221580505,\n", "    0.5763885378837585,\n", "    0.5763253569602966,\n", "    0.5761176347732544,\n", "    0.5760759711265564,\n", "    0.5746794939041138,\n", "    0.5744171738624573,\n", "    0.5742660760879517,\n", "    0.5742112398147583,\n", "    0.5742034316062927,\n", "    0.5740616321563721,\n", "    0.5733302235603333,\n", "    0.573292076587677,\n", "    0.5731571912765503,\n", "    0.5730592608451843,\n", "    0.5728392601013184,\n", "    0.571785569190979,\n", "    0.5713380575180054,\n", "    0.5710909366607666,\n", "    0.5707199573516846,\n", "    0.5706163644790649,\n", "    0.5705900192260742,\n", "    0.570338785648346,\n", "    0.5701417326927185,\n", "    0.5700944662094116,\n", "    0.5700834393501282,\n", "    0.5696247816085815,\n", "    0.5692822337150574,\n", "    0.5690827965736389,\n", "    0.5687150359153748,\n", "    0.5687146186828613,\n", "    0.5686818957328796,\n", "    0.5686447620391846,\n", "    0.5685743093490601,\n", "    0.568393886089325,\n", "    0.5682032108306885,\n", "    0.5679066181182861,\n", "    0.5678610801696777,\n", "    0.5675678849220276,\n", "    0.5675394535064697,\n", "    0.567408561706543,\n", "    0.5673738718032837,\n", "    0.5671758055686951,\n", "    0.5671340823173523,\n", "    0.5664903521537781,\n", "    0.5663324594497681,\n", "    0.5656479001045227,\n", "    0.5656325817108154,\n", "    0.5653063654899597,\n", "    0.565115213394165,\n", "    0.5649501085281372,\n", "    0.5642525553703308,\n", "    0.5642475485801697,\n", "    0.5642257928848267,\n", "    0.5641114711761475,\n", "    0.5639886856079102,\n", "    0.5639677047729492,\n", "    0.5632710456848145,\n", "    0.5632535815238953,\n", "    0.5631274580955505,\n", "    0.5630457401275635,\n", "    0.5628888607025146,\n", "    0.5627797245979309,\n", "    0.5624223947525024,\n", "    0.5615848302841187,\n", "    0.5611938834190369,\n", "    0.5609237551689148,\n", "    0.5605946183204651,\n", "    0.5605728626251221,\n", "    0.5603991746902466,\n", "    0.5595914125442505,\n", "    0.5592038631439209,\n", "    0.5582883358001709,\n", "    0.558074414730072,\n", "    0.5577468872070312,\n", "    0.5575985908508301,\n", "    0.5561418533325195,\n", "    0.5560917258262634,\n", "    0.5559461116790771,\n", "    0.5559022426605225,\n", "    0.5555617809295654,\n", "    0.5547317266464233,\n", "    0.5546111464500427,\n", "    0.554409921169281,\n", "    0.5542524456977844,\n", "    0.554079532623291,\n", "    0.5536198616027832,\n", "    0.5527538061141968,\n", "    0.5526108145713806,\n", "    0.5522387623786926,\n", "    0.5521010160446167,\n", "    0.5517719388008118,\n", "    0.5516143441200256,\n", "    0.5515637993812561,\n", "    0.5510571002960205,\n", "    0.5509266257286072,\n", "    0.5499669313430786,\n", "    0.5496559143066406,\n", "    0.5494126081466675,\n", "    0.5491641759872437,\n", "    0.548949122428894,\n", "    0.5488339066505432,\n", "    0.5484192967414856,\n", "    0.5482316017150879,\n", "    0.5481317639350891,\n", "    0.547918975353241,\n", "    0.5476598739624023,\n", "    0.5476452708244324,\n", "    0.5465521812438965,\n", "    0.5461713671684265,\n", "    0.5460586547851562,\n", "    0.5455039739608765,\n", "    0.5449166297912598,\n", "    0.5447160601615906,\n", "    0.5445075035095215,\n", "    0.5443755388259888,\n", "    0.5443150997161865,\n", "    0.5441897511482239,\n", "    0.5439751148223877,\n", "    0.5435760617256165,\n", "    0.5431958436965942,\n", "    0.5424712300300598,\n", "    0.5423347353935242,\n", "    0.5421316623687744,\n", "    0.5420617461204529,\n", "    0.5419756174087524,\n", "    0.5418910980224609,\n", "    0.5412149429321289,\n", "    0.54099440574646,\n", "    0.5407584309577942,\n", "    0.5406563878059387,\n", "    0.5403761267662048,\n", "    0.5403215289115906,\n", "    0.5400746464729309,\n", "    0.5387665629386902,\n", "    0.5386180281639099,\n", "    0.5386025905609131,\n", "    0.5385122299194336,\n", "    0.5378236174583435,\n", "    0.5375604033470154,\n", "    0.5373092889785767,\n", "    0.5371151566505432,\n", "    0.5370606184005737,\n", "    0.536771297454834,\n", "    0.5363240838050842,\n", "    0.535853922367096,\n", "    0.5358450412750244,\n", "    0.5357071757316589,\n", "    0.5355180501937866,\n", "    0.5352650284767151,\n", "    0.5348871350288391,\n", "    0.5344833135604858,\n", "    0.534108579158783,\n", "    0.5338484048843384,\n", "    0.5336177349090576,\n", "    0.5335980653762817,\n", "    0.5329223871231079,\n", "    0.532815158367157,\n", "    0.5323047637939453,\n", "    0.5321986079216003,\n", "    0.5321667790412903,\n", "    0.532084584236145,\n", "    0.5320664048194885,\n", "    0.5315571427345276,\n", "    0.5306642055511475,\n", "    0.5306017994880676,\n", "    0.530401885509491,\n", "    0.5301651954650879,\n", "    0.5299767851829529,\n", "    0.5297106504440308,\n", "    0.5295664668083191,\n", "    0.5294540524482727,\n", "    0.5285013318061829,\n", "    0.527971088886261,\n", "    0.5277310013771057,\n", "    0.5272039175033569,\n", "    0.5267593264579773,\n", "    0.5266623497009277,\n", "    0.5265195965766907,\n", "    0.5263415575027466,\n", "    0.5261741280555725,\n", "    0.5252991914749146,\n", "    0.524787187576294,\n", "    0.5244137048721313,\n", "    0.5240115523338318,\n", "    0.5238341689109802,\n", "    0.523729145526886,\n", "    0.5236253142356873,\n", "    0.5236173868179321,\n", "    0.5236107110977173,\n", "    0.5234154462814331,\n", "    0.5233871936798096,\n", "    0.52326899766922,\n", "    0.5229800939559937,\n", "    0.5229023694992065,\n", "    0.5225610136985779,\n", "    0.5223855376243591,\n", "    0.5222339034080505,\n", "    0.5219058394432068,\n", "    0.5217188000679016,\n", "    0.5214598178863525,\n", "    0.5210549831390381,\n", "    0.5209704041481018,\n", "    0.5207533836364746,\n", "    0.5207481980323792,\n", "    0.5203011631965637,\n", "    0.5199356079101562,\n", "    0.5193400382995605,\n", "    0.5192545056343079,\n", "    0.518627405166626,\n", "    0.5185922384262085,\n", "    0.518504798412323,\n", "    0.5183274745941162,\n", "    0.5182803273200989,\n", "    0.5178312659263611,\n", "    0.5175201892852783,\n", "    0.5174104571342468,\n", "    0.5172801613807678,\n", "    0.5165795683860779,\n", "    0.5161949396133423,\n", "    0.5160829424858093,\n", "    0.5157583355903625,\n", "    0.5146952271461487,\n", "    0.514634370803833,\n", "    0.5144887566566467,\n", "    0.513837993144989,\n", "    0.5138033628463745,\n", "    0.5132898092269897,\n", "    0.512828528881073,\n", "    0.512226402759552,\n", "    0.5120346546173096,\n", "    0.5119408369064331,\n", "    0.5119115710258484,\n", "    0.5117729306221008,\n", "    0.5117133259773254,\n", "    0.5116068720817566,\n", "    0.5099065899848938,\n", "    0.5098102688789368,\n", "    0.509421706199646,\n", "    0.509331226348877,\n", "    0.5087867975234985,\n", "    0.508700966835022,\n", "    0.5083475708961487,\n", "    0.5074193477630615,\n", "    0.5072686076164246,\n", "    0.5070876479148865,\n", "    0.5069858431816101,\n", "    0.5067065954208374,\n", "    0.5066980719566345,\n", "    0.5066868662834167,\n", "    0.5063809156417847,\n", "    0.5062867999076843,\n", "    0.5061664581298828,\n", "    0.5061010122299194,\n", "    0.5059512257575989,\n", "    0.5058633685112,\n", "    0.5055397748947144,\n", "    0.5055195689201355,\n", "    0.5042335391044617,\n", "    0.5040457844734192,\n", "    0.5039957165718079,\n", "    0.5037396550178528,\n", "    0.503696084022522,\n", "    0.5036721229553223,\n", "    0.5035803914070129,\n", "    0.5035223364830017,\n", "    0.5035102367401123,\n", "    0.503319501876831,\n", "    0.5030537843704224,\n", "    0.502881646156311,\n", "    0.5026463270187378,\n", "    0.5026099681854248,\n", "    0.5023906230926514,\n", "    0.5019810795783997,\n", "    0.5017000436782837,\n", "    0.5016398429870605,\n", "    0.5014907121658325,\n", "    0.5009934902191162,\n", "    0.5008249282836914,\n", "    0.4992403984069824,\n", "    0.49902158975601196,\n", "    0.49898016452789307,\n", "    0.4987703263759613,\n", "    0.49854663014411926,\n", "    0.49844881892204285,\n", "    0.4979209005832672,\n", "    0.4976659119129181,\n", "    0.49698808789253235,\n", "    0.49697765707969666,\n", "    0.4959806501865387,\n", "    0.49589264392852783,\n", "    0.49555298686027527,\n", "    0.4952860474586487,\n", "    0.4950627088546753,\n", "    0.49459749460220337,\n", "    0.4945169687271118,\n", "    0.4943888187408447,\n", "    0.49388551712036133,\n", "    0.49381646513938904,\n", "    0.49289557337760925,\n", "    0.4927968382835388,\n", "    0.492206871509552,\n", "    0.4920995235443115,\n", "    0.491729736328125,\n", "    0.4916738271713257,\n", "    0.49017706513404846,\n", "    0.48991072177886963,\n", "    0.4898253083229065,\n", "    0.48919716477394104,\n", "    0.488808274269104,\n", "    0.48797717690467834,\n", "    0.48771941661834717,\n", "    0.48756200075149536,\n", "    0.48673298954963684,\n", "    0.4861059784889221,\n", "    0.48586544394493103,\n", "    0.48577049374580383,\n", "    0.48524588346481323,\n", "    0.48509353399276733,\n", "    0.4848264455795288,\n", "    0.48479682207107544,\n", "    0.4843918979167938,\n", "    0.4840961694717407,\n", "    0.48408472537994385,\n", "    0.48392027616500854,\n", "    0.4837554693222046,\n", "    0.48342445492744446,\n", "    0.48336440324783325,\n", "    0.48309335112571716,\n", "    0.48269978165626526,\n", "    0.48178020119667053,\n", "    0.481712281703949,\n", "    0.4816063642501831,\n", "    0.48148995637893677,\n", "    0.48134657740592957,\n", "    0.48096975684165955,\n", "    0.48080670833587646,\n", "    0.48047706484794617,\n", "    0.4804496765136719,\n", "    0.4801945388317108,\n", "    0.48008403182029724,\n", "    0.47997257113456726,\n", "    0.4797312021255493,\n", "    0.4795404076576233,\n", "    0.47938987612724304,\n", "    0.479048490524292,\n", "    0.47897830605506897,\n", "    0.4789726138114929,\n", "    0.4789269268512726,\n", "    0.4785624146461487,\n", "    0.47828295826911926,\n", "    0.47784775495529175,\n", "    0.47729209065437317,\n", "    0.47728097438812256,\n", "    0.4769919514656067,\n", "    0.47572022676467896,\n", "    0.47521552443504333,\n", "    0.4749474823474884,\n", "    0.47479870915412903,\n", "    0.47436267137527466,\n", "    0.47413384914398193,\n", "    0.4740779995918274,\n", "    0.4739990830421448,\n", "    0.4739040732383728,\n", "    0.4733946919441223,\n", "    0.47288206219673157,\n", "    0.4726584553718567,\n", "    0.47238609194755554,\n", "    0.47227829694747925,\n", "    0.4719228446483612,\n", "    0.47134774923324585,\n", "    0.47121962904930115,\n", "    0.47024667263031006,\n", "    0.46982473134994507,\n", "    0.46961236000061035,\n", "    0.4695749580860138,\n", "    0.46951645612716675,\n", "    0.46921753883361816,\n", "    0.4688724875450134,\n", "    0.46876445412635803,\n", "    0.46819350123405457,\n", "    0.46808335185050964,\n", "    0.4676513075828552,\n", "    0.46708276867866516,\n", "    0.46699437499046326,\n", "    0.4668959081172943,\n", "    0.4668194353580475,\n", "    0.466781347990036,\n", "    0.4666384160518646,\n", "    0.466533362865448,\n", "    0.4664665460586548,\n", "    0.4663811922073364,\n", "    0.46603378653526306,\n", "    0.46597570180892944,\n", "    0.4656013250350952,\n", "    0.4651605188846588,\n", "    0.46383345127105713,\n", "    0.4632294178009033,\n", "    0.4631468951702118,\n", "    0.4628618657588959,\n", "    0.46272313594818115,\n", "    0.4627135992050171,\n", "    0.4624042809009552,\n", "    0.46205756068229675,\n", "    0.46198031306266785,\n", "    0.4619397521018982,\n", "    0.4616946578025818,\n", "    0.4616857171058655,\n", "    0.4615980386734009,\n", "    0.46143099665641785,\n", "    0.46122804284095764,\n", "    0.4609867334365845,\n", "    0.4606665074825287,\n", "    0.46060505509376526,\n", "    0.4602615535259247,\n", "    0.4600958228111267,\n", "    0.45975738763809204,\n", "    0.45974481105804443,\n", "    0.45962291955947876,\n", "    0.45884329080581665,\n", "    0.4588061571121216,\n", "    0.45855289697647095,\n", "    0.45850086212158203,\n", "    0.4584472179412842,\n", "    0.45840057730674744,\n", "    0.4583798348903656,\n", "    0.4580388069152832,\n", "    0.4570097327232361,\n", "    0.45697709918022156,\n", "    0.45654726028442383,\n", "    0.4564763605594635,\n", "    0.4562240540981293,\n", "    0.45595431327819824,\n", "    0.45586466789245605,\n", "    0.4548664689064026,\n", "    0.45336946845054626,\n", "    0.45302993059158325,\n", "    0.452940970659256,\n", "    0.4528767466545105,\n", "    0.4527047574520111,\n", "    0.4525086283683777,\n", "    0.4519267678260803,\n", "    0.451447457075119,\n", "    0.4508584141731262,\n", "    0.45084869861602783,\n", "    0.4507056474685669,\n", "    0.44878581166267395,\n", "    0.4485262930393219,\n", "    0.44845953583717346,\n", "    0.4482843577861786,\n", "    0.4477362632751465,\n", "    0.4474773406982422,\n", "    0.4474565088748932,\n", "    0.4473346471786499,\n", "    0.4471648931503296,\n", "    0.4470869302749634,\n", "    0.4470369815826416,\n", "    0.4469349980354309,\n", "    0.446559876203537,\n", "    0.44556212425231934,\n", "    0.44538480043411255,\n", "    0.4451623260974884,\n", "    0.4419635534286499,\n", "    0.44192832708358765,\n", "    0.4414258599281311,\n", "    0.44133394956588745,\n", "    0.441291868686676,\n", "    0.4412824809551239,\n", "    0.44084763526916504,\n", "    0.44019657373428345,\n", "    0.4399142265319824,\n", "    0.4392808675765991,\n", "    0.43926072120666504,\n", "    0.4388812184333801,\n", "    0.4382534623146057,\n", "    0.4364493787288666,\n", "    0.4362269639968872,\n", "    0.43573135137557983,\n", "    0.4351774752140045,\n", "    0.4348544478416443,\n", "    0.434664785861969,\n", "    0.4345231056213379,\n", "    0.43451595306396484,\n", "    0.4342617988586426,\n", "    0.43367186188697815,\n", "    0.43361997604370117,\n", "    0.4326038360595703,\n", "    0.43215876817703247,\n", "    0.4321386218070984,\n", "    0.43213045597076416,\n", "    0.4319225549697876,\n", "    0.43172743916511536,\n", "    0.4314207434654236,\n", "    0.4311297535896301,\n", "    0.43085193634033203,\n", "    0.43083280324935913,\n", "    0.43050748109817505,\n", "    0.43029746413230896,\n", "    0.4301360249519348,\n", "    0.43011462688446045,\n", "    0.4300631284713745,\n", "    0.42992621660232544,\n", "    0.4298848807811737,\n", "    0.4292154014110565,\n", "    0.4291706681251526,\n", "    0.4291327893733978,\n", "    0.42871952056884766,\n", "    0.4282774031162262,\n", "    0.42820000648498535,\n", "    0.4280369281768799,\n", "    0.4264754056930542,\n", "    0.42634642124176025,\n", "    0.42631375789642334,\n", "    0.42629510164260864,\n", "    0.4262930154800415,\n", "    0.4262787401676178,\n", "    0.4257465600967407,\n", "    0.42541301250457764,\n", "    0.4251987934112549,\n", "    0.4251331388950348,\n", "    0.42511123418807983,\n", "    0.4250136911869049,\n", "    0.424839586019516,\n", "    0.42474621534347534,\n", "    0.424600750207901,\n", "    0.42441847920417786,\n", "    0.4235896170139313,\n", "    0.42320963740348816,\n", "    0.42310062050819397,\n", "    0.4229313135147095,\n", "    0.42285460233688354,\n", "    0.42258015275001526,\n", "    0.42199015617370605,\n", "    0.42145806550979614,\n", "    0.42056480050086975,\n", "    0.4202522337436676,\n", "    0.42005038261413574,\n", "    0.42002394795417786,\n", "    0.4197964370250702,\n", "    0.41975656151771545,\n", "    0.41965144872665405,\n", "    0.4192579388618469,\n", "    0.4190945625305176,\n", "    0.41884949803352356,\n", "    0.4188269078731537,\n", "    0.4181503355503082,\n", "    0.4181372821331024,\n", "    0.4178203344345093,\n", "    0.4175206422805786,\n", "    0.41731855273246765,\n", "    0.41728100180625916,\n", "    0.4172349274158478,\n", "    0.4168108403682709,\n", "    0.416667103767395,\n", "    0.41621968150138855,\n", "    0.41613274812698364,\n", "    0.41590434312820435,\n", "    0.4158880114555359,\n", "    0.41554781794548035,\n", "    0.41513752937316895,\n", "    0.4149503707885742,\n", "    0.4147450625896454,\n", "    0.41456863284111023,\n", "    0.41381213068962097,\n", "    0.4136825203895569,\n", "    0.4135536253452301,\n", "    0.4125632047653198,\n", "    0.41235724091529846,\n", "    0.41218504309654236,\n", "    0.4119795262813568,\n", "    0.41184255480766296,\n", "    0.41122639179229736,\n", "    0.41055211424827576,\n", "    0.41037237644195557,\n", "    0.41030508279800415,\n", "    0.40981096029281616,\n", "    0.4091421961784363,\n", "    0.40911075472831726,\n", "    0.4089713990688324,\n", "    0.40878015756607056,\n", "    0.407977432012558,\n", "    0.40753263235092163,\n", "    0.4071584641933441,\n", "    0.40687814354896545,\n", "    0.40634432435035706,\n", "    0.406139612197876,\n", "    0.40609946846961975,\n", "    0.40551817417144775,\n", "    0.4054330587387085,\n", "    0.40465685725212097,\n", "    0.4046042263507843,\n", "    0.40453487634658813,\n", "    0.4044117331504822,\n", "    0.4044063091278076,\n", "    0.40424251556396484,\n", "    0.40399107336997986,\n", "    0.4032181203365326,\n", "    0.40274307131767273,\n", "    0.40181469917297363,\n", "    0.400782972574234,\n", "    0.40077710151672363,\n", "    0.4005860388278961,\n", "    0.40039926767349243,\n", "    0.40017998218536377,\n", "    0.39998215436935425,\n", "    0.3996635377407074,\n", "    0.3991306722164154,\n", "    0.3987979590892792,\n", "    0.39803239703178406,\n", "    0.3978213965892792,\n", "    0.3976932168006897,\n", "    0.397466242313385,\n", "    0.39701446890830994,\n", "    0.39695772528648376,\n", "    0.396789014339447,\n", "    0.3964296877384186,\n", "    0.3963863253593445,\n", "    0.39607852697372437,\n", "    0.3960008919239044,\n", "    0.39517128467559814,\n", "    0.3948304355144501,\n", "    0.39468204975128174,\n", "    0.3945874869823456,\n", "    0.39455971121788025,\n", "    0.3943758010864258,\n", "    0.3942587971687317,\n", "    0.3938363790512085,\n", "    0.39370834827423096,\n", "    0.39255985617637634,\n", "    0.39187732338905334,\n", "    0.3909900188446045,\n", "    0.390904039144516,\n", "    0.39053085446357727,\n", "    0.3905240297317505,\n", "    0.39042648673057556,\n", "    0.3902670443058014,\n", "    0.39017292857170105,\n", "    0.3899761140346527,\n", "    0.3897341191768646,\n", "    0.3896079361438751,\n", "    0.38920947909355164,\n", "    0.3885754346847534,\n", "    0.3879094421863556,\n", "    0.38769131898880005,\n", "    0.3873184025287628,\n", "    0.38716983795166016,\n", "    0.38688990473747253,\n", "    0.38603031635284424,\n", "    0.3859669268131256,\n", "    0.3859117329120636,\n", "    0.38532397150993347,\n", "    0.3848888874053955,\n", "    0.38453739881515503,\n", "    0.38286885619163513,\n", "    0.382575660943985,\n", "    0.38249048590660095,\n", "    0.3823443055152893,\n", "    0.38231831789016724,\n", "    0.38218170404434204,\n", "    0.38170579075813293,\n", "    0.38108110427856445,\n", "    0.38007161021232605,\n", "    0.37978237867355347,\n", "    0.379393994808197,\n", "    0.37885424494743347,\n", "    0.37858811020851135,\n", "    0.37782934308052063,\n", "    0.3775053024291992,\n", "    0.37746649980545044,\n", "    0.37640923261642456,\n", "    0.3763565123081207,\n", "    0.37619730830192566,\n", "    0.37585610151290894,\n", "    0.3745252788066864,\n", "    0.3744102418422699,\n", "    0.37373971939086914,\n", "    0.37346431612968445,\n", "    0.3733370006084442,\n", "    0.37326541543006897,\n", "    0.3731141984462738,\n", "    0.37282460927963257,\n", "    0.37239769101142883,\n", "    0.3712233006954193,\n", "    0.3711933493614197,\n", "    0.3711772859096527,\n", "    0.3707764744758606,\n", "    0.37048977613449097,\n", "    0.37008967995643616,\n", "    0.3699605166912079,\n", "    0.36985623836517334,\n", "    0.36936745047569275,\n", "    0.36880144476890564,\n", "    0.3686974048614502,\n", "    0.36779823899269104,\n", "    0.3677724003791809,\n", "    0.36765486001968384,\n", "    0.3663778007030487,\n", "    0.3656257092952728,\n", "    0.36547958850860596,\n", "    0.3647864758968353,\n", "    0.36450350284576416,\n", "    0.3644755482673645,\n", "    0.3643191456794739,\n", "    0.36405855417251587,\n", "    0.3633148968219757,\n", "    0.3626902997493744,\n", "    0.36227643489837646,\n", "    0.36201390624046326,\n", "    0.36197298765182495,\n", "    0.3617185652256012,\n", "    0.3616717457771301,\n", "    0.36150872707366943,\n", "    0.3607175052165985,\n", "    0.36067649722099304,\n", "    0.36044877767562866,\n", "    0.35940223932266235,\n", "    0.35939228534698486,\n", "    0.35867440700531006,\n", "    0.35834813117980957,\n", "    0.35761040449142456,\n", "    0.3566271662712097,\n", "    0.35658347606658936,\n", "    0.3562101423740387,\n", "    0.35614249110221863,\n", "    0.3559834659099579,\n", "    0.3559514284133911,\n", "    0.35583868622779846,\n", "    0.3557267189025879,\n", "    0.355528324842453,\n", "    0.35536760091781616,\n", "    0.3538789451122284,\n", "    0.35371437668800354,\n", "    0.3530821204185486,\n", "    0.3530634641647339,\n", "    0.35246211290359497,\n", "    0.3524012565612793,\n", "    0.3521853983402252,\n", "    0.35195109248161316,\n", "    0.35187163949012756,\n", "    0.3518490791320801,\n", "    0.3510468900203705,\n", "    0.3510158658027649,\n", "    0.35000115633010864,\n", "    0.3491481840610504,\n", "    0.3487222194671631,\n", "    0.3482969403266907,\n", "    0.34813544154167175,\n", "    0.34742480516433716,\n", "    0.3473971486091614,\n", "    0.3473901152610779,\n", "    0.3472094237804413,\n", "    0.3470272719860077,\n", "    0.3469645380973816,\n", "    0.34607169032096863,\n", "    0.3458537757396698,\n", "    0.3447803258895874,\n", "    0.34465861320495605,\n", "    0.34290453791618347,\n", "    0.34260743856430054,\n", "    0.34254416823387146,\n", "    0.34251126646995544,\n", "    0.34181082248687744,\n", "    0.34124886989593506,\n", "    0.3410014808177948,\n", "    0.3409765958786011,\n", "    0.33957549929618835,\n", "    0.3395363688468933,\n", "    0.3393372893333435,\n", "    0.33927032351493835,\n", "    0.3391669690608978,\n", "    0.3391049802303314,\n", "    0.33858630061149597,\n", "    0.33850544691085815,\n", "    0.33816879987716675,\n", "    0.3379656970500946,\n", "    0.3379332721233368,\n", "    0.33756202459335327,\n", "    0.33750951290130615,\n", "    0.33745962381362915,\n", "    0.33698970079421997,\n", "    0.33665573596954346,\n", "    0.3352607786655426,\n", "    0.334736704826355,\n", "    0.3346337378025055,\n", "    0.33400052785873413,\n", "    0.3339584767818451,\n", "    0.3339020013809204,\n", "    0.33360493183135986,\n", "    0.3335230350494385,\n", "    0.33346661925315857,\n", "    0.3332739472389221,\n", "    0.3330234885215759,\n", "    0.3329734802246094,\n", "    0.3318025767803192,\n", "    0.33058884739875793,\n", "    0.33055540919303894,\n", "    0.32958653569221497,\n", "    0.32907912135124207,\n", "    0.32892748713493347,\n", "    0.328490287065506,\n", "    0.32799291610717773,\n", "    0.3277624845504761,\n", "    0.32764145731925964,\n", "    0.32746177911758423,\n", "    0.3271627724170685,\n", "    0.32711392641067505,\n", "    0.3270532190799713,\n", "    0.32535290718078613,\n", "    0.32508477568626404,\n", "    0.32499539852142334,\n", "    0.3248407542705536,\n", "    0.3248033821582794,\n", "    0.32431140542030334,\n", "    0.3233656883239746,\n", "    0.32323506474494934,\n", "    0.3227858245372772,\n", "    0.3224281072616577,\n", "    0.3221152722835541,\n", "    0.32197484374046326,\n", "    0.3213147819042206,\n", "    0.3212965428829193,\n", "    0.32013845443725586,\n", "    0.32008787989616394,\n", "    0.31970539689064026,\n", "    0.31969591975212097,\n", "    0.3188915252685547,\n", "    0.31842389702796936,\n", "    0.3181261420249939,\n", "    0.31755417585372925,\n", "    0.3175504505634308,\n", "    0.31739407777786255,\n", "    0.3165769577026367,\n", "    0.3161444067955017,\n", "    0.3160806894302368,\n", "    0.31602349877357483,\n", "    0.3157857656478882,\n", "    0.31563541293144226,\n", "    0.31553977727890015,\n", "    0.3151482045650482,\n", "    0.3142046630382538,\n", "    0.31345006823539734,\n", "    0.3134211301803589,\n", "    0.3131430447101593,\n", "    0.31217479705810547,\n", "    0.3119758665561676,\n", "    0.31135693192481995,\n", "    0.31056004762649536,\n", "    0.31029003858566284,\n", "    0.3101913034915924,\n", "    0.3099331855773926,\n", "    0.30967992544174194,\n", "    0.30964088439941406,\n", "    0.30959728360176086,\n", "    0.3093574345111847,\n", "    0.309343159198761,\n", "    0.309323251247406,\n", "    0.30922120809555054,\n", "    0.30918097496032715,\n", "    0.30914774537086487,\n", "    0.3091258704662323,\n", "    0.30789709091186523,\n", "    0.30710089206695557,\n", "    0.3068067729473114,\n", "    0.30657580494880676,\n", "    0.3064599931240082,\n", "    0.305594265460968,\n", "    0.3055724501609802,\n", "    0.30542606115341187,\n", "    0.305357962846756,\n", "    0.3038763999938965,\n", "    0.30378133058547974,\n", "    0.3036973774433136,\n", "    0.3035374879837036,\n", "    0.3033004105091095,\n", "    0.30322200059890747,\n", "    0.3022773265838623,\n", "    0.30103161931037903,\n", "    0.3010275363922119,\n", "    0.3009259104728699,\n", "    0.3005710244178772,\n", "    0.30021509528160095,\n", "    0.2992442846298218,\n", "    0.2989838123321533,\n", "    0.2987128794193268,\n", "    0.29820695519447327,\n", "    0.29817354679107666,\n", "    0.2980842888355255,\n", "    0.29803192615509033,\n", "    0.29800575971603394,\n", "    0.2973747253417969,\n", "    0.297139048576355,\n", "    0.2970641553401947,\n", "    0.29705169796943665,\n", "    0.295806884765625,\n", "    0.2947099804878235,\n", "    0.29461637139320374,\n", "    0.2943934500217438,\n", "    0.2942925989627838,\n", "    0.2936987578868866,\n", "    0.29365453124046326,\n", "    0.2935560345649719,\n", "    0.29329434037208557,\n", "    0.29308658838272095,\n", "    0.293013334274292,\n", "    0.2921537756919861,\n", "    0.2919650375843048,\n", "    0.2919267416000366,\n", "    0.2912406921386719,\n", "    0.29116836190223694,\n", "    0.29067903757095337,\n", "    0.2906624376773834,\n", "    0.290633887052536,\n", "    0.2905784249305725,\n", "    0.2902747094631195,\n", "    0.29021814465522766,\n", "    0.290067583322525,\n", "    0.29006490111351013,\n", "    0.2898687422275543,\n", "    0.28978708386421204,\n", "    0.2895315885543823,\n", "    0.2894158661365509,\n", "    0.2890514135360718,\n", "    ...],\n", "   'bboxes': [[230.17166137695312,\n", "     3.819277286529541,\n", "     241.6036376953125,\n", "     15.46953010559082],\n", "    [155.44581604003906,\n", "     2.780280113220215,\n", "     168.15870666503906,\n", "     16.208786010742188],\n", "    [204.70689392089844,\n", "     166.90966796875,\n", "     215.8999481201172,\n", "     178.08966064453125],\n", "    [54.6164436340332,\n", "     275.7963562011719,\n", "     65.92555236816406,\n", "     286.9384460449219],\n", "    [93.65365600585938,\n", "     40.47637939453125,\n", "     104.69548034667969,\n", "     51.65161895751953],\n", "    [220.0347442626953, 175.32470703125, 232.05760192871094, 187.359130859375],\n", "    [63.32639694213867,\n", "     213.2908477783203,\n", "     74.54241180419922,\n", "     224.78929138183594],\n", "    [23.979326248168945,\n", "     24.719131469726562,\n", "     36.7563362121582,\n", "     37.54813003540039],\n", "    [267.5207214355469,\n", "     1.1752710342407227,\n", "     282.2373962402344,\n", "     13.26516056060791],\n", "    [252.3547821044922,\n", "     183.38961791992188,\n", "     263.7699279785156,\n", "     194.87698364257812],\n", "    [62.19272232055664,\n", "     125.20060729980469,\n", "     73.55422973632812,\n", "     136.6680145263672],\n", "    [149.1262664794922,\n", "     354.59014892578125,\n", "     162.24070739746094,\n", "     367.852294921875],\n", "    [215.2032928466797,\n", "     229.72543334960938,\n", "     226.0150909423828,\n", "     240.20166015625],\n", "    [69.17081451416016,\n", "     150.4113311767578,\n", "     80.26448822021484,\n", "     161.53211975097656],\n", "    [208.74835205078125,\n", "     246.973388671875,\n", "     219.41778564453125,\n", "     257.67205810546875],\n", "    [211.95652770996094,\n", "     199.4136199951172,\n", "     222.1898956298828,\n", "     209.21241760253906],\n", "    [158.07159423828125,\n", "     196.49415588378906,\n", "     168.99371337890625,\n", "     207.6589813232422],\n", "    [39.027137756347656,\n", "     176.309814453125,\n", "     50.61658477783203,\n", "     187.30352783203125],\n", "    [164.50799560546875,\n", "     85.40929412841797,\n", "     176.22708129882812,\n", "     96.86336517333984],\n", "    [43.79071044921875, 120.494140625, 55.44202423095703, 132.550048828125],\n", "    [96.31293487548828,\n", "     127.1236343383789,\n", "     107.55020904541016,\n", "     137.89950561523438],\n", "    [142.96890258789062,\n", "     136.35670471191406,\n", "     153.739013671875,\n", "     146.48716735839844],\n", "    [154.45932006835938,\n", "     340.972412109375,\n", "     168.36160278320312,\n", "     354.21112060546875],\n", "    [194.9869842529297,\n", "     47.900638580322266,\n", "     207.44374084472656,\n", "     60.57345199584961],\n", "    [237.79124450683594,\n", "     79.6596450805664,\n", "     248.8493194580078,\n", "     90.57176971435547],\n", "    [60.3151741027832,\n", "     134.18995666503906,\n", "     71.34248352050781,\n", "     144.67311096191406],\n", "    [255.1109619140625,\n", "     173.4641571044922,\n", "     266.34259033203125,\n", "     184.68238830566406],\n", "    [214.7833709716797, 190.21444702148438, 224.54087829589844, 200.17578125],\n", "    [221.05221557617188,\n", "     87.40274047851562,\n", "     230.80194091796875,\n", "     96.73394775390625],\n", "    [228.2136688232422,\n", "     16.36914825439453,\n", "     238.9933319091797,\n", "     27.499229431152344],\n", "    [219.72927856445312,\n", "     220.35594177246094,\n", "     231.21136474609375,\n", "     231.90455627441406],\n", "    [88.94127655029297,\n", "     142.62081909179688,\n", "     100.12084197998047,\n", "     153.54360961914062],\n", "    [249.9347381591797,\n", "     0.8148660659790039,\n", "     264.5594482421875,\n", "     13.118006706237793],\n", "    [206.89892578125,\n", "     341.17205810546875,\n", "     218.25860595703125,\n", "     352.10198974609375],\n", "    [30.383377075195312,\n", "     115.65548706054688,\n", "     42.28264617919922,\n", "     127.63789367675781],\n", "    [270.3435363769531,\n", "     182.80902099609375,\n", "     280.9375915527344,\n", "     193.78143310546875],\n", "    [22.376911163330078,\n", "     135.9040985107422,\n", "     34.46696853637695,\n", "     147.9832305908203],\n", "    [141.7841796875,\n", "     143.0397186279297,\n", "     152.36019897460938,\n", "     153.78623962402344],\n", "    [101.70284271240234,\n", "     199.23472595214844,\n", "     113.9526596069336,\n", "     211.80226135253906],\n", "    [268.3765869140625,\n", "     190.79788208007812,\n", "     278.97418212890625,\n", "     201.39862060546875],\n", "    [132.03475952148438,\n", "     309.5790100097656,\n", "     143.72125244140625,\n", "     321.0812072753906],\n", "    [239.22933959960938,\n", "     222.190185546875,\n", "     249.79852294921875,\n", "     232.8939208984375],\n", "    [357.4980773925781,\n", "     46.474220275878906,\n", "     368.8750305175781,\n", "     58.14910125732422],\n", "    [173.45074462890625,\n", "     5.946429252624512,\n", "     185.8275146484375,\n", "     19.304386138916016],\n", "    [181.3739776611328,\n", "     327.62091064453125,\n", "     192.61012268066406,\n", "     338.83648681640625],\n", "    [22.660442352294922,\n", "     360.2187805175781,\n", "     33.452457427978516,\n", "     370.9476013183594],\n", "    [152.02920532226562,\n", "     340.21990966796875,\n", "     165.26104736328125,\n", "     352.82806396484375],\n", "    [265.13482666015625,\n", "     101.3879165649414,\n", "     275.98968505859375,\n", "     111.79610443115234],\n", "    [191.33274841308594,\n", "     160.60650634765625,\n", "     202.1281280517578,\n", "     171.30087280273438],\n", "    [143.4716796875,\n", "     189.9392852783203,\n", "     154.15139770507812,\n", "     200.70240783691406],\n", "    [131.4089813232422,\n", "     215.7132568359375,\n", "     143.23924255371094,\n", "     227.171630859375],\n", "    [260.52447509765625,\n", "     302.1139221191406,\n", "     271.223876953125,\n", "     312.5396423339844],\n", "    [88.28765869140625,\n", "     240.1527557373047,\n", "     99.3143310546875,\n", "     250.35618591308594],\n", "    [30.655237197875977,\n", "     286.3459167480469,\n", "     43.324989318847656,\n", "     298.4036560058594],\n", "    [247.59690856933594,\n", "     96.76970672607422,\n", "     258.49664306640625,\n", "     107.25284576416016],\n", "    [55.201812744140625,\n", "     43.35126876831055,\n", "     66.86215209960938,\n", "     54.94789505004883],\n", "    [14.571348190307617,\n", "     278.3888854980469,\n", "     26.65972328186035,\n", "     290.2737121582031],\n", "    [51.08292007446289,\n", "     1.126546859741211,\n", "     65.61160278320312,\n", "     12.551651000976562],\n", "    [205.277099609375,\n", "     215.74610900878906,\n", "     216.05068969726562,\n", "     226.7295379638672],\n", "    [101.87879943847656,\n", "     255.66221618652344,\n", "     112.03019714355469,\n", "     265.8715515136719],\n", "    [149.17591857910156,\n", "     271.78057861328125,\n", "     160.0086212158203,\n", "     282.46240234375],\n", "    [212.86038208007812,\n", "     150.25265502929688,\n", "     224.2786865234375,\n", "     161.64089965820312],\n", "    [70.91961669921875,\n", "     191.6371307373047,\n", "     81.71022033691406,\n", "     202.34791564941406],\n", "    [285.7422180175781,\n", "     8.63421630859375,\n", "     296.6430358886719,\n", "     19.37510871887207],\n", "    [200.34727478027344,\n", "     230.4119110107422,\n", "     210.8034210205078,\n", "     240.57737731933594],\n", "    [120.59971618652344,\n", "     109.10513305664062,\n", "     131.7203826904297,\n", "     120.23786926269531],\n", "    [87.46814727783203,\n", "     5.401984214782715,\n", "     99.09148406982422,\n", "     17.147411346435547],\n", "    [271.9669189453125,\n", "     38.14208221435547,\n", "     282.166259765625,\n", "     48.144981384277344],\n", "    [112.00218963623047,\n", "     134.2362518310547,\n", "     122.47577667236328,\n", "     144.6852569580078],\n", "    [168.64505004882812,\n", "     213.65016174316406,\n", "     179.59771728515625,\n", "     224.3842010498047],\n", "    [21.71065330505371,\n", "     365.9767150878906,\n", "     32.80939865112305,\n", "     377.4595031738281],\n", "    [253.53152465820312,\n", "     320.2403259277344,\n", "     264.3573303222656,\n", "     331.0429382324219],\n", "    [230.7834014892578,\n", "     1.9998369216918945,\n", "     244.7627716064453,\n", "     12.722481727600098],\n", "    [222.32760620117188,\n", "     216.8688507080078,\n", "     233.61166381835938,\n", "     228.4150848388672],\n", "    [124.07353973388672,\n", "     238.4872283935547,\n", "     135.5013885498047,\n", "     249.59519958496094],\n", "    [166.5462646484375,\n", "     174.54844665527344,\n", "     176.83102416992188,\n", "     184.5459442138672],\n", "    [165.07054138183594,\n", "     271.7947692871094,\n", "     176.4976043701172,\n", "     283.2860412597656],\n", "    [115.72207641601562, 165.59765625, 126.92236328125, 176.80136108398438],\n", "    [156.7741241455078,\n", "     110.28040313720703,\n", "     168.9272003173828,\n", "     122.04497528076172],\n", "    [287.2908020019531,\n", "     40.021881103515625,\n", "     298.3637390136719,\n", "     50.77202606201172],\n", "    [198.92430114746094,\n", "     183.49696350097656,\n", "     209.25062561035156,\n", "     193.14906311035156],\n", "    [192.37747192382812,\n", "     247.05084228515625,\n", "     202.80575561523438,\n", "     257.0010986328125],\n", "    [174.13475036621094,\n", "     239.41941833496094,\n", "     185.3076629638672,\n", "     249.97398376464844],\n", "    [159.3047637939453,\n", "     194.0762481689453,\n", "     170.19163513183594,\n", "     205.11817932128906],\n", "    [45.00294494628906,\n", "     24.347187042236328,\n", "     57.168846130371094,\n", "     36.75830841064453],\n", "    [86.31265258789062,\n", "     247.70335388183594,\n", "     97.04649353027344,\n", "     258.32867431640625],\n", "    [236.91033935546875,\n", "     127.60493469238281,\n", "     247.7701416015625,\n", "     138.0816192626953],\n", "    [73.06722259521484,\n", "     94.15796661376953,\n", "     85.4466781616211,\n", "     106.23772430419922],\n", "    [230.4271240234375,\n", "     46.37363815307617,\n", "     242.18289184570312,\n", "     57.349971771240234],\n", "    [216.95217895507812,\n", "     182.460205078125,\n", "     227.89407348632812,\n", "     193.15127563476562],\n", "    [48.603538513183594,\n", "     207.74444580078125,\n", "     59.35371398925781,\n", "     218.17984008789062],\n", "    [51.458831787109375,\n", "     4.131984710693359,\n", "     63.575843811035156,\n", "     16.64655303955078],\n", "    [69.98369598388672,\n", "     335.3158264160156,\n", "     81.11481475830078,\n", "     346.1893615722656],\n", "    [158.50819396972656, 294.3927001953125, 168.7046661376953, 304.3408203125],\n", "    [261.85504150390625,\n", "     60.730018615722656,\n", "     273.3485107421875,\n", "     72.53649139404297],\n", "    [193.57186889648438,\n", "     102.5331802368164,\n", "     204.713623046875,\n", "     113.37427520751953],\n", "    [37.2709846496582,\n", "     53.493255615234375,\n", "     49.60956954956055,\n", "     65.3189697265625],\n", "    [118.60269165039062,\n", "     23.18675994873047,\n", "     129.3260498046875,\n", "     33.640594482421875],\n", "    [248.9225311279297,\n", "     4.269482135772705,\n", "     260.61370849609375,\n", "     16.048288345336914],\n", "    [158.1454620361328,\n", "     62.27336883544922,\n", "     168.16612243652344,\n", "     71.97061920166016],\n", "    [95.84638214111328,\n", "     264.2222900390625,\n", "     106.98091888427734,\n", "     274.905029296875],\n", "    [232.2011260986328,\n", "     238.64735412597656,\n", "     243.0722198486328,\n", "     249.2258758544922],\n", "    [135.2444610595703,\n", "     22.953144073486328,\n", "     146.70973205566406,\n", "     33.959327697753906],\n", "    [140.16641235351562,\n", "     151.47071838378906,\n", "     150.99273681640625,\n", "     162.17723083496094],\n", "    [144.08245849609375,\n", "     47.55107879638672,\n", "     154.61669921875,\n", "     57.966949462890625],\n", "    [198.63137817382812,\n", "     86.64430236816406,\n", "     209.93341064453125,\n", "     97.49209594726562],\n", "    [229.72926330566406,\n", "     286.5963439941406,\n", "     240.1819610595703,\n", "     296.6770935058594],\n", "    [190.5880584716797,\n", "     207.99945068359375,\n", "     201.06874084472656,\n", "     218.27993774414062],\n", "    [104.32464599609375,\n", "     248.17596435546875,\n", "     114.71226501464844,\n", "     257.9266357421875],\n", "    [244.3380126953125,\n", "     294.0384521484375,\n", "     255.78451538085938,\n", "     305.11669921875],\n", "    [117.77507781982422,\n", "     261.11614990234375,\n", "     128.37159729003906,\n", "     271.57110595703125],\n", "    [117.77074432373047, 159.2103729248047, 129.0625, 170.28160095214844],\n", "    [314.47796630859375,\n", "     17.083431243896484,\n", "     326.4652099609375,\n", "     29.12002182006836],\n", "    [220.07899475097656,\n", "     120.96043395996094,\n", "     231.45030212402344,\n", "     131.93016052246094],\n", "    [189.89788818359375,\n", "     303.29486083984375,\n", "     200.02218627929688,\n", "     313.2694091796875],\n", "    [53.575538635253906,\n", "     145.59014892578125,\n", "     65.00008392333984,\n", "     156.7413330078125],\n", "    [188.4455108642578,\n", "     14.495075225830078,\n", "     201.07847595214844,\n", "     26.98029327392578],\n", "    [206.9994354248047,\n", "     159.250244140625,\n", "     218.2394561767578,\n", "     170.23980712890625],\n", "    [212.67626953125, 6.600693702697754, 223.5418701171875, 17.61374282836914],\n", "    [277.3725280761719,\n", "     216.36343383789062,\n", "     288.0934753417969,\n", "     227.38046264648438],\n", "    [105.55245971679688,\n", "     3.945134162902832,\n", "     118.35604858398438,\n", "     16.944007873535156],\n", "    [38.720359802246094,\n", "     366.7359924316406,\n", "     50.35283660888672,\n", "     378.8881530761719],\n", "    [248.43980407714844,\n", "     191.29037475585938,\n", "     260.24761962890625,\n", "     202.77645874023438],\n", "    [15.695064544677734,\n", "     336.2598571777344,\n", "     26.19989776611328,\n", "     346.6293029785156],\n", "    [93.35973358154297,\n", "     135.5671844482422,\n", "     104.39583587646484,\n", "     146.67323303222656],\n", "    [165.32803344726562,\n", "     222.99305725097656,\n", "     176.40933227539062,\n", "     234.47743225097656],\n", "    [84.18836975097656,\n", "     55.039459228515625,\n", "     95.42619323730469,\n", "     65.93669128417969],\n", "    [12.479206085205078,\n", "     256.34271240234375,\n", "     23.384605407714844,\n", "     267.00396728515625],\n", "    [79.87422180175781,\n", "     30.344284057617188,\n", "     91.29402160644531,\n", "     41.597312927246094],\n", "    [125.2135009765625,\n", "     190.7381591796875,\n", "     136.08868408203125,\n", "     201.71929931640625],\n", "    [237.2020263671875,\n", "     38.319400787353516,\n", "     249.08480834960938,\n", "     49.836612701416016],\n", "    [136.4141082763672,\n", "     62.78256607055664,\n", "     147.57228088378906,\n", "     73.74858856201172],\n", "    [140.52725219726562,\n", "     199.1998748779297,\n", "     151.10931396484375,\n", "     209.60093688964844],\n", "    [108.79617309570312,\n", "     232.3144073486328,\n", "     119.55101013183594,\n", "     242.81028747558594],\n", "    [138.9140167236328,\n", "     247.2369842529297,\n", "     150.7187042236328,\n", "     258.6788635253906],\n", "    [152.83444213867188,\n", "     158.09405517578125,\n", "     164.2984619140625,\n", "     169.40231323242188],\n", "    [224.22268676757812,\n", "     208.94766235351562,\n", "     234.91339111328125,\n", "     219.11129760742188],\n", "    [39.171390533447266,\n", "     325.3479309082031,\n", "     50.04336166381836,\n", "     336.2990417480469],\n", "    [71.96149444580078,\n", "     47.367000579833984,\n", "     82.61156463623047,\n", "     57.509281158447266],\n", "    [31.025556564331055,\n", "     206.78338623046875,\n", "     42.57044219970703,\n", "     217.918212890625],\n", "    [174.9656524658203,\n", "     101.39399719238281,\n", "     185.4517364501953,\n", "     111.53437805175781],\n", "    [92.88371276855469,\n", "     43.125083923339844,\n", "     104.43083190917969,\n", "     54.62347412109375],\n", "    [262.285888671875,\n", "     294.4492492675781,\n", "     273.33062744140625,\n", "     305.4386901855469],\n", "    [117.83445739746094,\n", "     303.36016845703125,\n", "     128.04388427734375,\n", "     313.20556640625],\n", "    [12.817890167236328,\n", "     70.3980941772461,\n", "     24.69729995727539,\n", "     82.57550811767578],\n", "    [171.61021423339844,\n", "     207.86581420898438,\n", "     182.86167907714844,\n", "     219.07742309570312],\n", "    [175.7930908203125,\n", "     151.36546325683594,\n", "     187.27642822265625,\n", "     162.56263732910156],\n", "    [176.65087890625,\n", "     200.15684509277344,\n", "     187.79782104492188,\n", "     210.8841094970703],\n", "    [260.8340148925781,\n", "     22.99744987487793,\n", "     271.2512512207031,\n", "     33.108856201171875],\n", "    [76.38632202148438, 38.217918395996094, 87.09915161132812, 48.84033203125],\n", "    [131.67254638671875,\n", "     271.45367431640625,\n", "     142.334716796875,\n", "     282.05523681640625],\n", "    [47.85315704345703,\n", "     14.258331298828125,\n", "     59.90968322753906,\n", "     25.71392822265625],\n", "    [34.86473083496094,\n", "     287.8026428222656,\n", "     47.014678955078125,\n", "     299.6182556152344],\n", "    [29.44685173034668,\n", "     215.46572875976562,\n", "     40.56827163696289,\n", "     226.72207641601562],\n", "    [228.58518981933594,\n", "     248.0858917236328,\n", "     240.2797088623047,\n", "     259.6836242675781],\n", "    [101.14124298095703,\n", "     160.16104125976562,\n", "     111.82405853271484,\n", "     170.62469482421875],\n", "    [213.77635192871094,\n", "     102.10377502441406,\n", "     224.02247619628906,\n", "     112.09051513671875],\n", "    [149.48114013671875,\n", "     215.15550231933594,\n", "     160.45205688476562,\n", "     226.08079528808594],\n", "    [134.8369140625,\n", "     264.31011962890625,\n", "     145.53414916992188,\n", "     274.58233642578125],\n", "    [161.0594482421875, 86.39032745361328, 172.5, 97.36962127685547],\n", "    [39.04360580444336, 270.873046875, 50.15488052368164, 281.89227294921875],\n", "    [126.44357299804688,\n", "     47.527061462402344,\n", "     137.38003540039062,\n", "     58.742889404296875],\n", "    [87.09351348876953, 288.19317626953125, 98.4261245727539, 299.55322265625],\n", "    [111.99777221679688,\n", "     39.400447845458984,\n", "     122.4239501953125,\n", "     49.55526351928711],\n", "    [54.06783676147461,\n", "     239.17103576660156,\n", "     64.2585678100586,\n", "     248.9696807861328],\n", "    [28.83074951171875,\n", "     15.513336181640625,\n", "     40.42597198486328,\n", "     26.973403930664062],\n", "    [189.91140747070312,\n", "     63.85268783569336,\n", "     201.1497802734375,\n", "     74.956298828125],\n", "    [293.4139099121094,\n", "     214.61219787597656,\n", "     305.2756652832031,\n", "     226.98731994628906],\n", "    [174.96873474121094,\n", "     1.060831069946289,\n", "     189.6228790283203,\n", "     12.51565170288086],\n", "    [204.6431427001953, 311.14697265625, 214.8412628173828, 321.263916015625],\n", "    [2.092716693878174,\n", "     132.54071044921875,\n", "     16.751882553100586,\n", "     147.0670166015625],\n", "    [10.488472938537598,\n", "     110.31687927246094,\n", "     24.215003967285156,\n", "     124.352294921875],\n", "    [237.6592254638672,\n", "     143.28672790527344,\n", "     248.81639099121094,\n", "     154.08543395996094],\n", "    [124.1909408569336,\n", "     197.73428344726562,\n", "     134.76724243164062,\n", "     207.96368408203125],\n", "    [245.99085998535156,\n", "     110.86849212646484,\n", "     256.45361328125,\n", "     121.17932891845703],\n", "    [79.14958190917969,\n", "     127.33911895751953,\n", "     89.44827270507812,\n", "     137.48277282714844],\n", "    [51.356719970703125,\n", "     147.58993530273438,\n", "     63.152320861816406,\n", "     159.2227783203125],\n", "    [298.58807373046875,\n", "     197.566650390625,\n", "     310.91790771484375,\n", "     209.75994873046875],\n", "    [20.96431541442871,\n", "     46.70376968383789,\n", "     34.11522674560547,\n", "     59.842037200927734],\n", "    [238.37014770507812,\n", "     348.40478515625,\n", "     250.11904907226562,\n", "     360.50872802734375],\n", "    [77.00509643554688,\n", "     270.0657043457031,\n", "     88.24925231933594,\n", "     281.4354553222656],\n", "    [230.3154754638672,\n", "     192.190673828125,\n", "     240.72361755371094,\n", "     202.48968505859375],\n", "    [23.39798355102539,\n", "     310.7201232910156,\n", "     34.173545837402344,\n", "     321.2676086425781],\n", "    [262.23785400390625,\n", "     254.3353729248047,\n", "     273.02386474609375,\n", "     265.13458251953125],\n", "    [195.6443634033203,\n", "     96.66695404052734,\n", "     206.6640167236328,\n", "     107.43720245361328],\n", "    [172.53067016601562,\n", "     116.6094741821289,\n", "     184.79867553710938,\n", "     128.6011505126953],\n", "    [29.030363082885742,\n", "     255.2339630126953,\n", "     40.43348693847656,\n", "     266.6662902832031],\n", "    [263.734619140625,\n", "     56.708839416503906,\n", "     274.94830322265625,\n", "     67.9384994506836],\n", "    [2.7138819694519043,\n", "     366.0766906738281,\n", "     15.985374450683594,\n", "     379.2565612792969],\n", "    [12.261567115783691,\n", "     160.38172912597656,\n", "     24.378585815429688,\n", "     172.69422912597656],\n", "    [62.94503402709961,\n", "     30.35311508178711,\n", "     74.57185363769531,\n", "     41.63850784301758],\n", "    [31.88693618774414,\n", "     113.84999084472656,\n", "     44.08456039428711,\n", "     125.93325805664062],\n", "    [1.7123565673828125,\n", "     317.8114318847656,\n", "     14.579046249389648,\n", "     330.0367736816406],\n", "    [207.73489379882812,\n", "     112.63675689697266,\n", "     218.8714599609375,\n", "     123.59467315673828],\n", "    [30.395980834960938, 342.326171875, 41.34343719482422, 353.41534423828125],\n", "    [261.5151672363281,\n", "     214.0042266845703,\n", "     272.1722106933594,\n", "     224.65382385253906],\n", "    [169.2432861328125,\n", "     166.47561645507812,\n", "     180.53305053710938,\n", "     177.57763671875],\n", "    [212.15496826171875,\n", "     0.542356014251709,\n", "     225.21884155273438,\n", "     10.873903274536133],\n", "    [149.41526794433594,\n", "     32.32133483886719,\n", "     160.78321838378906,\n", "     43.83654022216797],\n", "    [263.260009765625,\n", "     207.75875854492188,\n", "     274.29693603515625,\n", "     218.46795654296875],\n", "    [221.9249267578125, 79.2497787475586, 232.1630859375, 89.36312103271484],\n", "    [204.6425323486328,\n", "     23.55678367614746,\n", "     216.1920623779297,\n", "     35.41759490966797],\n", "    [136.39385986328125,\n", "     254.52818298339844,\n", "     147.47775268554688,\n", "     265.099853515625],\n", "    [223.18663024902344,\n", "     252.8972930908203,\n", "     235.46681213378906,\n", "     264.9524230957031],\n", "    [166.3946075439453,\n", "     82.10073852539062,\n", "     178.2360076904297,\n", "     93.76249694824219],\n", "    [255.3505096435547,\n", "     32.566986083984375,\n", "     266.1921691894531,\n", "     42.85517120361328],\n", "    [200.71063232421875,\n", "     175.69886779785156,\n", "     211.0792236328125,\n", "     185.57020568847656],\n", "    [284.8443603515625,\n", "     245.6515350341797,\n", "     295.53509521484375,\n", "     256.7270202636719],\n", "    [0.6366715431213379,\n", "     55.09067916870117,\n", "     12.936592102050781,\n", "     68.95880126953125],\n", "    [141.7642059326172,\n", "     101.86973571777344,\n", "     153.75953674316406,\n", "     113.48704528808594],\n", "    [99.78718566894531,\n", "     317.4855041503906,\n", "     113.27641296386719,\n", "     330.3201599121094],\n", "    [134.58946228027344,\n", "     168.00558471679688,\n", "     145.14503479003906,\n", "     178.700439453125],\n", "    [61.47016143798828, 222.39694213867188, 72.36023712158203, 233.5205078125],\n", "    [21.557945251464844,\n", "     138.64620971679688,\n", "     33.27635192871094,\n", "     150.45083618164062],\n", "    [230.3467559814453,\n", "     104.63290405273438,\n", "     240.7815399169922,\n", "     114.69931030273438],\n", "    [78.66515350341797,\n", "     265.316650390625,\n", "     89.7860336303711,\n", "     276.31243896484375],\n", "    [47.10924530029297,\n", "     214.22471618652344,\n", "     57.59984588623047,\n", "     224.90211486816406],\n", "    [207.9271240234375,\n", "     207.39321899414062,\n", "     218.33987426757812,\n", "     217.56292724609375],\n", "    [255.47781372070312,\n", "     169.97119140625,\n", "     266.4041442871094,\n", "     180.68597412109375],\n", "    [98.682373046875,\n", "     262.28131103515625,\n", "     109.38510131835938,\n", "     272.62139892578125],\n", "    [279.715087890625,\n", "     206.85394287109375,\n", "     289.984130859375,\n", "     217.22442626953125],\n", "    [176.12986755371094,\n", "     333.6837463378906,\n", "     187.7908172607422,\n", "     344.9895935058594],\n", "    [227.3373260498047,\n", "     159.9823760986328,\n", "     237.3407440185547,\n", "     169.9807586669922],\n", "    [127.92240905761719,\n", "     136.2836456298828,\n", "     138.13951110839844,\n", "     146.03416442871094],\n", "    [151.3563232421875,\n", "     208.22650146484375,\n", "     161.73736572265625,\n", "     218.25680541992188],\n", "    [197.41403198242188,\n", "     279.4995422363281,\n", "     207.98751831054688,\n", "     290.2102355957031],\n", "    [86.70992279052734,\n", "     333.7344055175781,\n", "     98.22713470458984,\n", "     345.2126159667969],\n", "    [239.09584045410156,\n", "     174.74427795410156,\n", "     250.01609802246094,\n", "     185.6651153564453],\n", "    [184.30551147460938,\n", "     318.24383544921875,\n", "     195.55868530273438,\n", "     329.236328125],\n", "    [112.39695739746094,\n", "     127.91949462890625,\n", "     122.90798950195312,\n", "     138.37353515625],\n", "    [284.37652587890625,\n", "     96.23929595947266,\n", "     295.38507080078125,\n", "     106.84545135498047],\n", "    [140.65634155273438,\n", "     -0.45024538040161133,\n", "     153.92608642578125,\n", "     9.927736282348633],\n", "    [9.532689094543457,\n", "     115.1815185546875,\n", "     23.179763793945312,\n", "     128.62893676757812],\n", "    [271.0876770019531,\n", "     318.545166015625,\n", "     282.0630187988281,\n", "     329.40899658203125],\n", "    [200.30345153808594,\n", "     78.94651794433594,\n", "     212.0823211669922,\n", "     90.47164916992188],\n", "    [182.23130798339844, 87.92822265625, 192.7800750732422, 98.11280822753906],\n", "    [261.8113098144531,\n", "     343.1910705566406,\n", "     273.1850891113281,\n", "     355.0810241699219],\n", "    [223.1846160888672,\n", "     168.11648559570312,\n", "     233.8930206298828,\n", "     178.70547485351562],\n", "    [127.69957733154297,\n", "     76.62959289550781,\n", "     139.30764770507812,\n", "     87.70635986328125],\n", "    [100.19464111328125,\n", "     59.17507553100586,\n", "     111.82598876953125,\n", "     70.60645294189453],\n", "    [257.8392639160156,\n", "     224.75755310058594,\n", "     268.8767395019531,\n", "     235.0154266357422],\n", "    [82.863525390625, 22.488426208496094, 94.5985107421875, 33.87750244140625],\n", "    [42.1391716003418,\n", "     124.78446960449219,\n", "     54.10585403442383,\n", "     137.0076446533203],\n", "    [174.37718200683594,\n", "     296.3273620605469,\n", "     184.3980255126953,\n", "     305.9527893066406],\n", "    [141.6875, 56.42213821411133, 152.3359375, 67.2130355834961],\n", "    [93.17926788330078,\n", "     182.14810180664062,\n", "     102.84569549560547,\n", "     191.677490234375],\n", "    [303.0577087402344,\n", "     46.00070571899414,\n", "     313.5661926269531,\n", "     56.315921783447266],\n", "    [215.27670288085938,\n", "     96.98268127441406,\n", "     225.278076171875,\n", "     106.58676147460938],\n", "    [253.07199096679688,\n", "     85.44237518310547,\n", "     264.1645812988281,\n", "     96.53595733642578],\n", "    [18.664487838745117,\n", "     0.5254559516906738,\n", "     31.995038986206055,\n", "     10.407329559326172],\n", "    [118.33024597167969,\n", "     64.64047241210938,\n", "     129.28713989257812,\n", "     75.58680725097656],\n", "    [188.70895385742188,\n", "     165.6282958984375,\n", "     199.40341186523438,\n", "     175.95443725585938],\n", "    [64.24847412109375,\n", "     117.9560317993164,\n", "     76.82888793945312,\n", "     130.33763122558594],\n", "    [142.48388671875,\n", "     227.59170532226562,\n", "     154.04391479492188,\n", "     238.95913696289062],\n", "    [302.383056640625,\n", "     8.76352310180664,\n", "     312.5992431640625,\n", "     19.083988189697266],\n", "    [93.43302917480469,\n", "     225.23480224609375,\n", "     104.78631591796875,\n", "     236.33566284179688],\n", "    [187.36732482910156,\n", "     312.3788146972656,\n", "     198.00611877441406,\n", "     322.6970520019531],\n", "    [166.91384887695312,\n", "     263.31402587890625,\n", "     177.74258422851562,\n", "     273.755126953125],\n", "    [211.6964569091797,\n", "     14.275497436523438,\n", "     222.53114318847656,\n", "     24.863574981689453],\n", "    [125.77674865722656,\n", "     143.8848114013672,\n", "     136.38148498535156,\n", "     154.3623809814453],\n", "    [245.72250366210938,\n", "     16.70586395263672,\n", "     257.0545959472656,\n", "     28.32939910888672],\n", "    [242.87255859375, 303.09521484375, 253.70779418945312, 313.24310302734375],\n", "    [45.37837600708008,\n", "     350.11041259765625,\n", "     56.11556625366211,\n", "     361.0321044921875],\n", "    [85.06169128417969,\n", "     15.34597396850586,\n", "     96.65728759765625,\n", "     27.119464874267578],\n", "    [100.06353759765625,\n", "     111.94415283203125,\n", "     111.59715270996094,\n", "     123.57797241210938],\n", "    [76.91199493408203,\n", "     134.9351043701172,\n", "     87.41071319580078,\n", "     145.40858459472656],\n", "    [326.2245178222656,\n", "     127.05005645751953,\n", "     336.9419250488281,\n", "     137.78549194335938],\n", "    [29.2535400390625,\n", "     159.72628784179688,\n", "     41.050628662109375,\n", "     171.421630859375],\n", "    [166.1418914794922,\n", "     319.1012268066406,\n", "     176.3769073486328,\n", "     329.2084655761719],\n", "    [182.6416778564453,\n", "     278.9787902832031,\n", "     193.0977020263672,\n", "     289.5792541503906],\n", "    [247.49832153320312,\n", "     102.27779388427734,\n", "     258.1044006347656,\n", "     112.83716583251953],\n", "    [93.80131530761719,\n", "     270.93572998046875,\n", "     104.78509521484375,\n", "     281.830322265625],\n", "    [228.91769409179688,\n", "     337.14373779296875,\n", "     239.68377685546875,\n", "     347.78704833984375],\n", "    [103.17141723632812,\n", "     102.64672088623047,\n", "     114.50186157226562,\n", "     113.5782241821289],\n", "    [171.70579528808594,\n", "     158.36318969726562,\n", "     183.3822479248047,\n", "     169.91891479492188],\n", "    [204.53611755371094,\n", "     69.31849670410156,\n", "     216.9171905517578,\n", "     81.63055419921875],\n", "    [153.044189453125,\n", "     255.859619140625,\n", "     164.41067504882812,\n", "     266.6026611328125],\n", "    [253.86129760742188,\n", "     358.5240478515625,\n", "     264.6875305175781,\n", "     369.35736083984375],\n", "    [318.92095947265625,\n", "     85.75223541259766,\n", "     330.383056640625,\n", "     96.8274917602539],\n", "    [236.534423828125,\n", "     231.2440948486328,\n", "     246.94100952148438,\n", "     241.56724548339844],\n", "    [125.11277770996094,\n", "     325.5709228515625,\n", "     137.4960174560547,\n", "     337.73675537109375],\n", "    [241.2507781982422, 303.876220703125, 252.32960510253906, 314.42236328125],\n", "    [89.69788360595703,\n", "     1.090282917022705,\n", "     103.80001068115234,\n", "     12.844011306762695],\n", "    [184.69932556152344,\n", "     223.82212829589844,\n", "     195.2750701904297,\n", "     234.41343688964844],\n", "    [16.063400268554688,\n", "     7.750532150268555,\n", "     26.698936462402344,\n", "     18.368616104125977],\n", "    [108.8830795288086,\n", "     48.19902801513672,\n", "     119.84101104736328,\n", "     59.40375518798828],\n", "    [73.4938735961914,\n", "     88.85550689697266,\n", "     85.95296478271484,\n", "     101.33293914794922],\n", "    [164.62156677246094,\n", "     46.96651077270508,\n", "     175.48497009277344,\n", "     57.34267807006836],\n", "    [256.0658874511719,\n", "     129.17283630371094,\n", "     266.2641906738281,\n", "     138.88377380371094],\n", "    [80.96156311035156,\n", "     167.64613342285156,\n", "     91.99824523925781,\n", "     178.12852478027344],\n", "    [94.91206359863281,\n", "     174.94839477539062,\n", "     105.30715942382812,\n", "     185.42623901367188],\n", "    [223.85057067871094,\n", "     342.58795166015625,\n", "     234.07212829589844,\n", "     353.11431884765625],\n", "    [4.503787994384766,\n", "     85.29828643798828,\n", "     18.151657104492188,\n", "     99.22808074951172],\n", "    [142.0190887451172,\n", "     287.7142639160156,\n", "     152.92897033691406,\n", "     298.1917419433594],\n", "    [294.7905578613281,\n", "     21.332523345947266,\n", "     305.8316345214844,\n", "     32.042484283447266],\n", "    [36.3673210144043,\n", "     0.3686347007751465,\n", "     49.78874588012695,\n", "     10.621932983398438],\n", "    [102.68498229980469,\n", "     295.4349060058594,\n", "     113.93814086914062,\n", "     306.4417419433594],\n", "    [156.0628662109375,\n", "     71.7127685546875,\n", "     166.28707885742188,\n", "     81.39408874511719],\n", "    [71.95974731445312,\n", "     1.0169219970703125,\n", "     85.49856567382812,\n", "     11.141216278076172],\n", "    [100.02416229248047,\n", "     203.3438262939453,\n", "     112.3669662475586,\n", "     215.6432647705078],\n", "    [56.74515914916992,\n", "     273.2168273925781,\n", "     67.91200256347656,\n", "     284.5771789550781],\n", "    [191.8575897216797,\n", "     295.8474426269531,\n", "     201.6243438720703,\n", "     305.2584533691406],\n", "    [100.15460205078125,\n", "     342.2179260253906,\n", "     112.40528869628906,\n", "     354.1519470214844],\n", "    [84.11856842041016,\n", "     253.1489715576172,\n", "     94.95153045654297,\n", "     263.4375305175781],\n", "    [182.3721466064453,\n", "     229.1448211669922,\n", "     192.80235290527344,\n", "     239.4337615966797],\n", "    [129.87109375, 217.1351776123047, 141.29205322265625, 228.2068634033203],\n", "    [214.16696166992188,\n", "     278.8766784667969,\n", "     225.44882202148438,\n", "     289.8468322753906],\n", "    [287.74761962890625,\n", "     239.1455078125,\n", "     298.6307373046875,\n", "     250.43511962890625],\n", "    [118.865234375,\n", "     257.55218505859375,\n", "     129.37188720703125,\n", "     268.03131103515625],\n", "    [308.2474060058594,\n", "     30.165042877197266,\n", "     320.2224426269531,\n", "     41.89670944213867],\n", "    [246.7357940673828,\n", "     198.44830322265625,\n", "     258.26397705078125,\n", "     209.9114990234375],\n", "    [80.60975646972656,\n", "     23.138900756835938,\n", "     92.85911560058594,\n", "     35.184478759765625],\n", "    [300.27056884765625,\n", "     15.06143856048584,\n", "     311.27178955078125,\n", "     26.264328002929688],\n", "    [67.60311889648438,\n", "     302.16400146484375,\n", "     80.65428161621094,\n", "     314.77203369140625],\n", "    [297.3536376953125,\n", "     106.97891998291016,\n", "     309.40240478515625,\n", "     118.66634368896484],\n", "    [229.18853759765625,\n", "     109.34636688232422,\n", "     239.64419555664062,\n", "     119.29999542236328],\n", "    [251.8280487060547,\n", "     237.7535858154297,\n", "     262.46929931640625,\n", "     248.2395782470703],\n", "    [269.0301818847656,\n", "     240.346923828125,\n", "     279.8268127441406,\n", "     251.63433837890625],\n", "    [3.3616886138916016,\n", "     36.919822692871094,\n", "     16.77001190185547,\n", "     49.75446319580078],\n", "    [283.7538146972656,\n", "     191.57078552246094,\n", "     295.4896545410156,\n", "     203.46449279785156],\n", "    [17.190622329711914,\n", "     191.6914825439453,\n", "     28.501768112182617,\n", "     202.59877014160156],\n", "    [264.5949401855469,\n", "     199.43353271484375,\n", "     275.5769958496094,\n", "     209.97412109375],\n", "    [70.49354553222656,\n", "     239.36099243164062,\n", "     81.41072082519531,\n", "     249.7052001953125],\n", "    [71.10487365722656,\n", "     102.4212875366211,\n", "     82.66464233398438,\n", "     113.81438446044922],\n", "    [86.48551940917969,\n", "     110.62043762207031,\n", "     96.32159423828125,\n", "     120.31222534179688],\n", "    [0.3252744674682617,\n", "     270.7987365722656,\n", "     12.447338104248047,\n", "     285.0518493652344],\n", "    [243.981201171875, 20.346630096435547, 255.462890625, 31.484352111816406],\n", "    [151.5554962158203,\n", "     311.76617431640625,\n", "     162.0164031982422,\n", "     322.150634765625],\n", "    [182.2376708984375,\n", "     184.19163513183594,\n", "     192.72018432617188,\n", "     194.19578552246094],\n", "    [122.37852478027344,\n", "     286.08099365234375,\n", "     134.39878845214844,\n", "     297.814208984375],\n", "    [277.89483642578125,\n", "     157.85531616210938,\n", "     291.62200927734375,\n", "     171.24978637695312],\n", "    [27.573780059814453,\n", "     348.8416442871094,\n", "     38.5086555480957,\n", "     359.5306091308594],\n", "    [135.76136779785156,\n", "     332.39080810546875,\n", "     147.99549865722656,\n", "     343.9710693359375],\n", "    [24.40229034423828,\n", "     262.9776611328125,\n", "     35.982879638671875,\n", "     274.28070068359375],\n", "    [213.17913818359375,\n", "     135.3332061767578,\n", "     225.4083251953125,\n", "     146.9246368408203],\n", "    [312.2944641113281,\n", "     21.290849685668945,\n", "     324.4273376464844,\n", "     33.615257263183594],\n", "    [160.28256225585938,\n", "     142.33692932128906,\n", "     170.6907958984375,\n", "     152.35389709472656],\n", "    [44.43678283691406,\n", "     263.7285461425781,\n", "     55.104957580566406,\n", "     274.3952941894531],\n", "    [71.80767059326172,\n", "     6.6302924156188965,\n", "     83.04596710205078,\n", "     18.038496017456055],\n", "    [139.60330200195312,\n", "     333.06915283203125,\n", "     152.54678344726562,\n", "     345.69451904296875],\n", "    [109.26306915283203,\n", "     320.09686279296875,\n", "     121.98722076416016,\n", "     332.39862060546875],\n", "    [300.8694152832031,\n", "     167.23086547851562,\n", "     315.1828308105469,\n", "     181.0748291015625],\n", "    [59.958656311035156,\n", "     317.38824462890625,\n", "     71.4516830444336,\n", "     328.72955322265625],\n", "    [253.65744018554688,\n", "     39.01295852661133,\n", "     264.4983215332031,\n", "     49.9063606262207],\n", "    [3.817420482635498,\n", "     308.9639587402344,\n", "     17.193981170654297,\n", "     321.7870178222656],\n", "    [115.93110656738281,\n", "     352.4374084472656,\n", "     127.54991149902344,\n", "     364.2063903808594],\n", "    [125.93791198730469,\n", "     280.11688232421875,\n", "     137.9954071044922,\n", "     291.80462646484375],\n", "    [13.177410125732422,\n", "     341.885986328125,\n", "     23.76168441772461,\n", "     352.49639892578125],\n", "    [197.82464599609375,\n", "     129.08847045898438,\n", "     209.36880493164062,\n", "     140.116455078125],\n", "    [273.51788330078125,\n", "     312.5303039550781,\n", "     284.0076904296875,\n", "     322.5787048339844],\n", "    [-1.0463571548461914,\n", "     334.3135681152344,\n", "     9.428226470947266,\n", "     347.2748107910156],\n", "    [206.74574279785156,\n", "     303.9446716308594,\n", "     216.7604522705078,\n", "     313.8078308105469],\n", "    [157.21766662597656,\n", "     304.39300537109375,\n", "     167.03334045410156,\n", "     313.984619140625],\n", "    [294.4877624511719,\n", "     163.80908203125,\n", "     307.4788513183594,\n", "     176.69937133789062],\n", "    [165.5766143798828,\n", "     114.0216293334961,\n", "     177.66786193847656,\n", "     125.73824310302734],\n", "    [194.66506958007812,\n", "     51.25699234008789,\n", "     207.3421630859375,\n", "     63.79887771606445],\n", "    [71.08983612060547,\n", "     324.9920349121094,\n", "     82.2662582397461,\n", "     336.1371154785156],\n", "    [309.5090637207031,\n", "     63.4251708984375,\n", "     320.8180847167969,\n", "     74.71218872070312],\n", "    [63.159507751464844, 300.9306640625, 76.3069839477539, 313.3389892578125],\n", "    [282.4912109375,\n", "     198.27334594726562,\n", "     293.46295166015625,\n", "     209.17935180664062],\n", "    [221.93576049804688,\n", "     349.6604309082031,\n", "     232.4810791015625,\n", "     360.3486633300781],\n", "    [53.46891403198242,\n", "     93.7968978881836,\n", "     64.7652816772461,\n", "     104.95415496826172],\n", "    [59.44683074951172,\n", "     61.51329803466797,\n", "     72.45430755615234,\n", "     74.00606536865234],\n", "    [24.856014251708984,\n", "     222.21408081054688,\n", "     36.04143142700195,\n", "     232.99459838867188],\n", "    [266.77630615234375,\n", "     93.9877700805664,\n", "     278.61041259765625,\n", "     105.61861419677734],\n", "    [140.4231719970703,\n", "     294.2429504394531,\n", "     151.3000030517578,\n", "     304.6511535644531],\n", "    [279.1871032714844,\n", "     118.60044860839844,\n", "     289.2690124511719,\n", "     128.56210327148438],\n", "    [117.71116638183594,\n", "     208.88400268554688,\n", "     128.72340393066406,\n", "     219.9515380859375],\n", "    [124.37574768066406,\n", "     94.78350067138672,\n", "     136.8069305419922,\n", "     106.37186431884766],\n", "    [61.83857727050781,\n", "     263.9949645996094,\n", "     72.67657470703125,\n", "     274.8435363769531],\n", "    [117.6006851196289,\n", "     119.74579620361328,\n", "     128.33355712890625,\n", "     130.64459228515625],\n", "    [196.3843231201172,\n", "     0.4028596878051758,\n", "     209.02159118652344,\n", "     10.132758140563965],\n", "    [75.78569793701172,\n", "     184.41403198242188,\n", "     85.6971664428711,\n", "     193.63424682617188],\n", "    [163.7609100341797,\n", "     183.03697204589844,\n", "     173.8813018798828,\n", "     192.74754333496094],\n", "    [267.33392333984375,\n", "     245.08815002441406,\n", "     278.5228271484375,\n", "     256.1750183105469],\n", "    [91.74463653564453,\n", "     327.7164611816406,\n", "     104.25687408447266,\n", "     339.8952941894531],\n", "    [172.67446899414062, 303.77099609375, 183.3446044921875, 314.1533203125],\n", "    [284.3262023925781,\n", "     54.510162353515625,\n", "     294.9660339355469,\n", "     64.50005340576172],\n", "    [108.39551544189453,\n", "     238.1387939453125,\n", "     119.20970916748047,\n", "     248.39138793945312],\n", "    [281.4925231933594,\n", "     103.49417877197266,\n", "     292.2424621582031,\n", "     113.94341278076172],\n", "    [56.39047622680664,\n", "     87.75108337402344,\n", "     68.13111114501953,\n", "     99.14259338378906],\n", "    [-1.094343900680542,\n", "     5.529574394226074,\n", "     9.020767211914062,\n", "     18.180683135986328],\n", "    [144.12655639648438,\n", "     186.27857971191406,\n", "     154.6474609375,\n", "     196.46229553222656],\n", "    [22.411781311035156,\n", "     51.93292236328125,\n", "     35.05064392089844,\n", "     64.75590515136719],\n", "    [103.07138061523438,\n", "     15.994915008544922,\n", "     115.22183227539062,\n", "     27.72785186767578],\n", "    [-0.0351109504699707,\n", "     57.93733596801758,\n", "     11.047842025756836,\n", "     71.74620819091797],\n", "    [19.00419807434082,\n", "     280.94268798828125,\n", "     30.973276138305664,\n", "     292.420166015625],\n", "    [102.75883483886719,\n", "     55.58982849121094,\n", "     114.45429992675781,\n", "     67.38560485839844],\n", "    [117.13021850585938,\n", "     213.35011291503906,\n", "     127.88931274414062,\n", "     223.92503356933594],\n", "    [132.49407958984375, 172.9605712890625, 143.07723999023438, 183.369140625],\n", "    [157.8451385498047,\n", "     55.09673309326172,\n", "     168.2521514892578,\n", "     65.38750457763672],\n", "    [-1.152087688446045,\n", "     292.2905578613281,\n", "     10.81932258605957,\n", "     306.6932067871094],\n", "    [54.40968704223633,\n", "     59.13544464111328,\n", "     67.32022857666016,\n", "     71.6969985961914],\n", "    [207.19703674316406,\n", "     251.79788208007812,\n", "     217.75392150878906,\n", "     262.4816589355469],\n", "    [188.3335723876953,\n", "     124.42160034179688,\n", "     200.2981414794922,\n", "     135.81369018554688],\n", "    [21.3606014251709,\n", "     319.5127868652344,\n", "     31.785425186157227,\n", "     330.1459655761719],\n", "    [34.984127044677734,\n", "     101.61444854736328,\n", "     47.00016403198242,\n", "     113.12574005126953],\n", "    [363.8632507324219,\n", "     32.25867462158203,\n", "     376.7684020996094,\n", "     45.446205139160156],\n", "    [229.2831573486328,\n", "     280.3111267089844,\n", "     239.3357696533203,\n", "     290.1227111816406],\n", "    [73.80561828613281,\n", "     278.6024169921875,\n", "     84.60275268554688,\n", "     288.967529296875],\n", "    [47.049156188964844,\n", "     293.64654541015625,\n", "     59.23127746582031,\n", "     305.56976318359375],\n", "    [60.20051193237305, 173.26828002929688, 71.5499496459961, 184.521484375],\n", "    [173.7993927001953,\n", "     71.70500183105469,\n", "     183.8743133544922,\n", "     81.45960998535156],\n", "    [354.98980712890625,\n", "     53.45051193237305,\n", "     366.5911865234375,\n", "     64.79290008544922],\n", "    [134.82247924804688, 305.25634765625, 145.77572631835938, 316.4052734375],\n", "    [116.9137954711914,\n", "     31.40886878967285,\n", "     127.1117935180664,\n", "     41.2745361328125],\n", "    [174.3914794921875,\n", "     62.44548034667969,\n", "     184.49884033203125,\n", "     72.45265197753906],\n", "    [84.81600189208984,\n", "     198.0094451904297,\n", "     95.83817291259766,\n", "     209.13877868652344],\n", "    [110.49285888671875,\n", "     143.25204467773438,\n", "     120.76934814453125,\n", "     153.52108764648438],\n", "    [253.62380981445312,\n", "     267.7747802734375,\n", "     266.0105895996094,\n", "     280.18975830078125],\n", "    [237.9777374267578, 317.5537109375, 248.05445861816406, 327.5238037109375],\n", "    [107.48992156982422,\n", "     372.4833068847656,\n", "     121.2348861694336,\n", "     383.7860412597656],\n", "    [198.81375122070312,\n", "     325.4862976074219,\n", "     209.68478393554688,\n", "     336.4785461425781],\n", "    [129.81362915039062,\n", "     310.50775146484375,\n", "     140.98739624023438,\n", "     321.09027099609375],\n", "    [252.18954467773438,\n", "     148.4895477294922,\n", "     263.8290710449219,\n", "     159.59519958496094],\n", "    [362.3907165527344,\n", "     34.25419616699219,\n", "     375.1991271972656,\n", "     47.20115661621094],\n", "    [197.07769775390625,\n", "     7.270688533782959,\n", "     207.64041137695312,\n", "     17.816564559936523],\n", "    [40.790157318115234,\n", "     361.5982971191406,\n", "     51.74973678588867,\n", "     372.2804260253906],\n", "    [59.875335693359375,\n", "     80.40857696533203,\n", "     71.12361145019531,\n", "     91.78592681884766],\n", "    [169.55918884277344,\n", "     160.92514038085938,\n", "     180.6250762939453,\n", "     172.00485229492188],\n", "    [1.1139864921569824,\n", "     34.75628662109375,\n", "     13.870431900024414,\n", "     49.000999450683594],\n", "    [266.15576171875,\n", "     96.12801361083984,\n", "     277.5093994140625,\n", "     107.52664947509766],\n", "    [98.12499237060547,\n", "     119.42223358154297,\n", "     108.9822006225586,\n", "     130.1414337158203],\n", "    [148.77699279785156,\n", "     37.003055572509766,\n", "     160.1835479736328,\n", "     48.27872848510742],\n", "    [56.789302825927734,\n", "     184.7627716064453,\n", "     68.42141723632812,\n", "     196.04100036621094],\n", "    [77.74102783203125,\n", "     220.4425811767578,\n", "     88.6929931640625,\n", "     231.07542419433594],\n", "    [29.01523208618164, 302.7628173828125, 40.803470611572266, 314.3544921875],\n", "    [200.95584106445312,\n", "     320.4591369628906,\n", "     211.8262939453125,\n", "     331.3357238769531],\n", "    [43.46783447265625,\n", "     27.532432556152344,\n", "     55.62737274169922,\n", "     39.56703186035156],\n", "    [31.27968978881836,\n", "     338.6120300292969,\n", "     42.06917190551758,\n", "     348.7556457519531],\n", "    [148.8169708251953,\n", "     317.3851318359375,\n", "     159.1280059814453,\n", "     327.278564453125],\n", "    [35.77265167236328,\n", "     371.36480712890625,\n", "     49.26335144042969,\n", "     382.9976806640625],\n", "    [94.50435638427734,\n", "     78.55807495117188,\n", "     107.09049224853516,\n", "     90.36167907714844],\n", "    [58.79505157470703,\n", "     85.48002624511719,\n", "     70.30506134033203,\n", "     96.63032531738281],\n", "    [100.7016830444336,\n", "     22.054784774780273,\n", "     112.07367706298828,\n", "     33.0147705078125],\n", "    [161.8218231201172,\n", "     184.8266143798828,\n", "     171.5203399658203,\n", "     194.12754821777344],\n", "    [185.1920166015625,\n", "     216.75355529785156,\n", "     195.59591674804688,\n", "     226.8506317138672],\n", "    [179.82220458984375,\n", "     285.676025390625,\n", "     189.950927734375,\n", "     295.20745849609375],\n", "    [27.41107177734375,\n", "     164.7564697265625,\n", "     39.05609130859375,\n", "     176.26333618164062],\n", "    [254.76731872558594,\n", "     230.75997924804688,\n", "     265.4134521484375,\n", "     241.38308715820312],\n", "    [64.5318374633789,\n", "     113.30387878417969,\n", "     76.7265396118164,\n", "     125.16725158691406],\n", "    [341.3290710449219,\n", "     37.37261199951172,\n", "     353.7687683105469,\n", "     50.15929412841797],\n", "    [135.611572265625,\n", "     207.6624755859375,\n", "     146.61819458007812,\n", "     218.3331298828125],\n", "    [146.7170867919922,\n", "     80.03430938720703,\n", "     158.42811584472656,\n", "     91.69693756103516],\n", "    [184.750732421875,\n", "     175.71482849121094,\n", "     195.57281494140625,\n", "     185.8162078857422],\n", "    [178.11993408203125,\n", "     287.2018737792969,\n", "     188.102783203125,\n", "     296.7846374511719],\n", "    [120.78512573242188,\n", "     13.579368591308594,\n", "     132.2545928955078,\n", "     24.927841186523438],\n", "    [27.441787719726562,\n", "     220.7003631591797,\n", "     38.32671356201172,\n", "     231.2581024169922],\n", "    [209.91537475585938,\n", "     200.62078857421875,\n", "     220.18038940429688,\n", "     210.51931762695312],\n", "    [192.90109252929688,\n", "     151.89306640625,\n", "     203.269775390625,\n", "     161.85147094726562],\n", "    [47.953636169433594,\n", "     343.9165954589844,\n", "     58.959938049316406,\n", "     354.8901062011719],\n", "    [215.58143615722656,\n", "     327.7757568359375,\n", "     226.28318786621094,\n", "     338.38641357421875],\n", "    [279.77069091796875,\n", "     21.259140014648438,\n", "     290.6959228515625,\n", "     31.841453552246094],\n", "    [206.7602081298828,\n", "     115.66706848144531,\n", "     218.0127410888672,\n", "     126.72238159179688],\n", "    [352.6759948730469,\n", "     55.56865692138672,\n", "     363.7070007324219,\n", "     65.90926361083984],\n", "    [179.45782470703125,\n", "     190.59852600097656,\n", "     189.52224731445312,\n", "     200.62449645996094],\n", "    [120.8160171508789,\n", "     247.83950805664062,\n", "     131.45321655273438,\n", "     257.7881774902344],\n", "    [219.1807861328125,\n", "     223.1386260986328,\n", "     229.98065185546875,\n", "     234.2370147705078],\n", "    [95.19723510742188, 316.0067138671875, 108.5897216796875, 329.0595703125],\n", "    [134.67208862304688,\n", "     351.6897888183594,\n", "     145.46044921875,\n", "     362.6316223144531],\n", "    [145.0763397216797,\n", "     82.39381408691406,\n", "     156.3855438232422,\n", "     93.51100158691406],\n", "    [129.45663452148438,\n", "     271.91094970703125,\n", "     140.71661376953125,\n", "     282.6787109375],\n", "    [151.87371826171875,\n", "     108.19886779785156,\n", "     163.85678100585938,\n", "     119.71173095703125],\n", "    [196.3835906982422,\n", "     336.4665832519531,\n", "     207.13673400878906,\n", "     346.9732360839844],\n", "    [19.897153854370117,\n", "     237.72946166992188,\n", "     30.788793563842773,\n", "     248.26986694335938],\n", "    [365.60906982421875,\n", "     77.71659851074219,\n", "     376.9534912109375,\n", "     89.33636474609375],\n", "    [269.9410705566406,\n", "     135.4486541748047,\n", "     280.3945007324219,\n", "     145.58055114746094],\n", "    [212.39207458496094,\n", "     112.0321044921875,\n", "     222.91233825683594,\n", "     122.1209716796875],\n", "    [137.065673828125,\n", "     159.4608612060547,\n", "     147.71072387695312,\n", "     169.68357849121094],\n", "    [367.3439636230469,\n", "     68.6620864868164,\n", "     379.5599060058594,\n", "     80.85970306396484],\n", "    [181.93899536132812,\n", "     351.1836242675781,\n", "     195.694091796875,\n", "     364.6177062988281],\n", "    [241.41787719726562,\n", "     217.43373107910156,\n", "     252.15484619140625,\n", "     228.07737731933594],\n", "    [207.6676788330078,\n", "     62.68708038330078,\n", "     220.55116271972656,\n", "     75.60811614990234],\n", "    [19.946895599365234,\n", "     372.45330810546875,\n", "     32.72577667236328,\n", "     382.97039794921875],\n", "    [175.5943603515625,\n", "     55.613250732421875,\n", "     186.06201171875,\n", "     65.89346313476562],\n", "    [0.5170755386352539,\n", "     325.456787109375,\n", "     11.681443214416504,\n", "     338.3092041015625],\n", "    [170.51133728027344,\n", "     165.1282501220703,\n", "     181.62464904785156,\n", "     175.6269989013672],\n", "    [292.6568908691406,\n", "     71.29165649414062,\n", "     303.7118835449219,\n", "     82.26478576660156],\n", "    [62.18732452392578,\n", "     168.95420837402344,\n", "     73.36357879638672,\n", "     179.85276794433594],\n", "    [236.14822387695312,\n", "     181.57278442382812,\n", "     246.72286987304688,\n", "     191.75961303710938],\n", "    [149.82077026367188,\n", "     174.34036254882812,\n", "     159.70806884765625,\n", "     183.7393798828125],\n", "    [332.9461669921875,\n", "     55.253482818603516,\n", "     344.7569580078125,\n", "     67.06416320800781],\n", "    [212.02735900878906,\n", "     285.050048828125,\n", "     222.81568908691406,\n", "     295.1612548828125],\n", "    [195.8567352294922,\n", "     151.1844940185547,\n", "     206.7387237548828,\n", "     161.30247497558594],\n", "    [9.542427062988281,\n", "     257.1997375488281,\n", "     20.67617416381836,\n", "     267.8451843261719],\n", "    [14.981374740600586,\n", "     44.454097747802734,\n", "     27.719789505004883,\n", "     56.87485122680664],\n", "    [341.575927734375, 8.407734870910645, 353.72412109375, 20.863182067871094],\n", "    [47.73686981201172,\n", "     256.192626953125,\n", "     57.98175811767578,\n", "     266.45672607421875],\n", "    [197.97157287597656, 359.01123046875, 211.61170959472656, 372.5791015625],\n", "    [228.9320831298828,\n", "     295.2912902832031,\n", "     239.01268005371094,\n", "     304.7178649902344],\n", "    [160.8393096923828,\n", "     288.5475769042969,\n", "     171.62379455566406,\n", "     298.8242492675781],\n", "    [294.9475402832031,\n", "     206.5284881591797,\n", "     306.7442932128906,\n", "     218.67112731933594],\n", "    [69.04122924804688,\n", "     371.8242492675781,\n", "     83.02078247070312,\n", "     383.3956604003906],\n", "    [236.71424865722656,\n", "     133.70277404785156,\n", "     247.3789520263672,\n", "     144.51316833496094],\n", "    [62.6320915222168, 351.642578125, 73.7710189819336, 362.53863525390625],\n", "    [274.9192199707031,\n", "     310.76324462890625,\n", "     286.9814758300781,\n", "     322.2735595703125],\n", "    [35.4321174621582,\n", "     144.96731567382812,\n", "     46.37935256958008,\n", "     155.92654418945312],\n", "    [185.0111541748047,\n", "     20.540420532226562,\n", "     197.21803283691406,\n", "     32.418724060058594],\n", "    [109.91998291015625,\n", "     280.5766296386719,\n", "     120.47712707519531,\n", "     290.9345397949219],\n", "    [208.6102752685547,\n", "     296.5993957519531,\n", "     218.83766174316406,\n", "     306.4272155761719],\n", "    [277.6153564453125,\n", "     300.4559631347656,\n", "     288.59149169921875,\n", "     311.7135314941406],\n", "    [119.6888656616211,\n", "     344.41400146484375,\n", "     130.12997436523438,\n", "     354.209716796875],\n", "    [249.4795684814453,\n", "     188.423583984375,\n", "     261.1712646484375,\n", "     199.67926025390625],\n", "    [206.37374877929688,\n", "     132.6065673828125,\n", "     218.20718383789062,\n", "     144.145263671875],\n", "    [46.78467559814453,\n", "     110.69598388671875,\n", "     57.42188262939453,\n", "     121.45381164550781],\n", "    [249.66207885742188,\n", "     238.80352783203125,\n", "     259.9333190917969,\n", "     248.847900390625],\n", "    [195.7897186279297,\n", "     197.684326171875,\n", "     205.95387268066406,\n", "     207.27764892578125],\n", "    [140.31983947753906,\n", "     372.3232727050781,\n", "     154.37413024902344,\n", "     383.8945617675781],\n", "    [1.6104826927185059,\n", "     229.70411682128906,\n", "     15.184921264648438,\n", "     243.2130889892578],\n", "    [-0.14585351943969727,\n", "     149.1369171142578,\n", "     11.157270431518555,\n", "     163.06544494628906],\n", "    [308.7015075683594,\n", "     126.90101623535156,\n", "     319.7185363769531,\n", "     137.3747100830078],\n", "    [202.06454467773438,\n", "     77.19196319580078,\n", "     213.73410034179688,\n", "     88.23738861083984],\n", "    [44.67262268066406,\n", "     222.4369354248047,\n", "     54.82183837890625,\n", "     232.34010314941406],\n", "    [85.25965118408203,\n", "     343.2242736816406,\n", "     96.39427947998047,\n", "     353.9441833496094],\n", "    [317.9189453125,\n", "     0.8649773597717285,\n", "     331.51690673828125,\n", "     10.70146369934082],\n", "    [216.786376953125,\n", "     35.59041976928711,\n", "     228.34072875976562,\n", "     46.91752243041992],\n", "    [279.4825439453125,\n", "     16.742990493774414,\n", "     291.14031982421875,\n", "     27.766908645629883],\n", "    [25.037700653076172,\n", "     351.1834411621094,\n", "     35.55979537963867,\n", "     361.5178527832031],\n", "    [65.70687866210938,\n", "     111.08892822265625,\n", "     78.32907104492188,\n", "     123.45790100097656],\n", "    [186.7078857421875,\n", "     353.21112060546875,\n", "     200.34930419921875,\n", "     366.90093994140625],\n", "    [68.87786865234375,\n", "     14.882476806640625,\n", "     80.12532043457031,\n", "     26.25808334350586],\n", "    [260.5805969238281, 117.71484375, 270.7194519042969, 127.35159301757812],\n", "    [185.56053161621094,\n", "     313.33538818359375,\n", "     196.70448303222656,\n", "     324.35076904296875],\n", "    [1.0147075653076172,\n", "     189.18463134765625,\n", "     13.370843887329102,\n", "     203.10171508789062],\n", "    [116.40211486816406,\n", "     68.46892547607422,\n", "     127.74778747558594,\n", "     79.74292755126953],\n", "    [169.89349365234375,\n", "     210.92176818847656,\n", "     180.64398193359375,\n", "     221.4298553466797],\n", "    [234.19512939453125,\n", "     87.77991485595703,\n", "     244.36447143554688,\n", "     97.61222076416016],\n", "    [54.164756774902344,\n", "     300.1026611328125,\n", "     66.16848754882812,\n", "     311.5665283203125],\n", "    [348.7522888183594,\n", "     13.241325378417969,\n", "     362.0391540527344,\n", "     25.81118392944336],\n", "    [89.82041931152344,\n", "     140.6111297607422,\n", "     101.023193359375,\n", "     151.3395233154297],\n", "    [104.84779357910156,\n", "     192.3772430419922,\n", "     115.86602783203125,\n", "     203.3696746826172],\n", "    [118.23379516601562,\n", "     323.98583984375,\n", "     130.65386962890625,\n", "     336.03875732421875],\n", "    [219.8482208251953,\n", "     138.6973876953125,\n", "     231.42271423339844,\n", "     149.96328735351562],\n", "    [87.25898742675781,\n", "     151.12001037597656,\n", "     98.14448547363281,\n", "     162.08299255371094],\n", "    [201.92543029785156,\n", "     224.2956085205078,\n", "     212.77830505371094,\n", "     235.1184539794922],\n", "    [316.53778076171875,\n", "     173.59622192382812,\n", "     328.55084228515625,\n", "     184.86740112304688],\n", "    [187.9691619873047,\n", "     175.4961700439453,\n", "     197.56993103027344,\n", "     184.5028533935547],\n", "    [61.306610107421875,\n", "     357.6155700683594,\n", "     72.54203796386719,\n", "     368.6553649902344],\n", "    [297.96673583984375,\n", "     149.23541259765625,\n", "     309.13397216796875,\n", "     160.24716186523438],\n", "    [109.02497100830078,\n", "     182.6112518310547,\n", "     120.18065643310547,\n", "     193.7887725830078],\n", "    [240.17405700683594,\n", "     312.05413818359375,\n", "     250.77565002441406,\n", "     322.5518798828125],\n", "    [76.88906860351562,\n", "     359.83978271484375,\n", "     88.15968322753906,\n", "     370.90594482421875],\n", "    [163.328857421875,\n", "     275.7534484863281,\n", "     174.68490600585938,\n", "     287.1679382324219],\n", "    [195.79409790039062,\n", "     239.60433959960938,\n", "     205.47238159179688,\n", "     249.21035766601562],\n", "    [186.93531799316406,\n", "     215.0638885498047,\n", "     197.56056213378906,\n", "     225.5298614501953],\n", "    [338.451416015625,\n", "     94.17973327636719,\n", "     350.52593994140625,\n", "     106.45069885253906],\n", "    [184.71990966796875,\n", "     271.8157653808594,\n", "     195.32821655273438,\n", "     282.3865661621094],\n", "    [40.93988800048828,\n", "     319.7683410644531,\n", "     52.4378662109375,\n", "     331.2226867675781],\n", "    [50.27908706665039,\n", "     149.3438720703125,\n", "     61.2905158996582,\n", "     160.11895751953125],\n", "    [75.69038391113281, 277.70489501953125, 86.28462219238281, 288.0126953125],\n", "    [44.855751037597656,\n", "     56.190608978271484,\n", "     56.68256378173828,\n", "     67.61318969726562],\n", "    [5.090914726257324,\n", "     357.51568603515625,\n", "     17.560211181640625,\n", "     370.06719970703125],\n", "    [152.12376403808594,\n", "     166.65789794921875,\n", "     162.68870544433594,\n", "     177.13348388671875],\n", "    [92.77226257324219, 228.3139190673828, 104.2529296875, 239.9201202392578],\n", "    [2.8465137481689453,\n", "     224.54396057128906,\n", "     15.639955520629883,\n", "     236.76707458496094],\n", "    [108.6781234741211,\n", "     363.4848937988281,\n", "     120.82584381103516,\n", "     376.6452941894531],\n", "    [133.00967407226562,\n", "     70.98334503173828,\n", "     143.78775024414062,\n", "     82.4680404663086],\n", "    [245.33250427246094,\n", "     264.2511901855469,\n", "     256.3872375488281,\n", "     275.1760559082031],\n", "    [248.33041381835938,\n", "     248.1701202392578,\n", "     258.7590637207031,\n", "     258.28973388671875],\n", "    [124.5513687133789,\n", "     368.3055419921875,\n", "     136.59335327148438,\n", "     381.2181396484375],\n", "    [198.9912872314453,\n", "     271.6142272949219,\n", "     209.05686950683594,\n", "     281.6236267089844],\n", "    [273.1991882324219,\n", "     223.1314239501953,\n", "     283.7464904785156,\n", "     233.4633331298828],\n", "    [133.23492431640625,\n", "     127.02338409423828,\n", "     143.6162109375,\n", "     136.90325927734375],\n", "    [324.7524719238281,\n", "     2.0173745155334473,\n", "     339.5321960449219,\n", "     13.101619720458984],\n", "    [268.3906555175781,\n", "     153.35159301757812,\n", "     279.7305603027344,\n", "     164.52484130859375],\n", "    [0.03146076202392578,\n", "     0.10239458084106445,\n", "     12.86722469329834,\n", "     11.737871170043945],\n", "    [71.61548614501953,\n", "     144.9387664794922,\n", "     82.60742950439453,\n", "     155.70558166503906],\n", "    [286.9507751464844,\n", "     136.83641052246094,\n", "     296.8700256347656,\n", "     146.7528533935547],\n", "    [57.80258560180664,\n", "     134.95211791992188,\n", "     68.96524047851562,\n", "     145.53506469726562],\n", "    [263.1376953125,\n", "     150.88613891601562,\n", "     274.43536376953125,\n", "     161.62185668945312],\n", "    [196.35302734375,\n", "     192.13262939453125,\n", "     206.62857055664062,\n", "     202.33010864257812],\n", "    [269.7812194824219,\n", "     89.13137817382812,\n", "     281.6194763183594,\n", "     100.77195739746094],\n", "    [293.2889404296875,\n", "     128.53614807128906,\n", "     303.1636962890625,\n", "     137.9373016357422],\n", "    [20.451271057128906,\n", "     233.50718688964844,\n", "     31.558486938476562,\n", "     244.73329162597656],\n", "    [82.68472290039062,\n", "     28.93404769897461,\n", "     93.63009643554688,\n", "     39.24826431274414],\n", "    [208.55711364746094,\n", "     16.750699996948242,\n", "     220.32362365722656,\n", "     27.92488670349121],\n", "    [84.96043395996094,\n", "     309.2876892089844,\n", "     97.7103271484375,\n", "     321.7312316894531],\n", "    [120.66436767578125,\n", "     93.45511627197266,\n", "     132.0044708251953,\n", "     104.45133209228516],\n", "    [16.683324813842773,\n", "     327.4846496582031,\n", "     26.94754981994629,\n", "     337.5969543457031],\n", "    [11.519705772399902,\n", "     162.7480926513672,\n", "     23.80382537841797,\n", "     174.9955291748047],\n", "    [291.11285400390625,\n", "     220.06430053710938,\n", "     302.76904296875,\n", "     232.49954223632812],\n", "    [37.88195037841797,\n", "     96.8517837524414,\n", "     49.232337951660156,\n", "     108.12195587158203],\n", "    [79.44161224365234,\n", "     69.9525146484375,\n", "     91.80901336669922,\n", "     81.45988464355469],\n", "    [343.7266845703125,\n", "     31.959197998046875,\n", "     356.2279052734375,\n", "     44.41424560546875],\n", "    [286.0657043457031, 280.403564453125, 298.2505798339844, 292.8427734375],\n", "    [49.072486877441406,\n", "     12.713071823120117,\n", "     60.53528594970703,\n", "     23.542505264282227],\n", "    [278.916748046875, 262.05291748046875, 289.4666748046875, 272.8056640625],\n", "    [78.66015625, 308.3211364746094, 90.89389038085938, 320.2943420410156],\n", "    [304.06689453125,\n", "     136.74090576171875,\n", "     315.8673095703125,\n", "     148.2657470703125],\n", "    [285.3728332519531, 45.50263214111328, 296.2375793457031, 56.46923828125],\n", "    [160.08349609375,\n", "     327.4720764160156,\n", "     170.61093139648438,\n", "     337.6069641113281],\n", "    [150.12879943847656,\n", "     338.99078369140625,\n", "     162.8610382080078,\n", "     351.85028076171875],\n", "    [3.3208703994750977,\n", "     274.9851379394531,\n", "     16.56739044189453,\n", "     287.4034118652344],\n", "    [240.7102813720703,\n", "     207.22286987304688,\n", "     251.03199768066406,\n", "     217.29971313476562],\n", "    [233.91392517089844,\n", "     94.81602478027344,\n", "     243.71852111816406,\n", "     104.32229614257812],\n", "    [6.5908074378967285,\n", "     303.39031982421875,\n", "     20.129358291625977,\n", "     316.4813232421875],\n", "    [165.5070037841797,\n", "     30.41811180114746,\n", "     177.82664489746094,\n", "     42.278621673583984],\n", "    [253.11251831054688,\n", "     45.08219909667969,\n", "     263.5572204589844,\n", "     55.27903747558594],\n", "    [79.3794937133789, 351.57672119140625, 90.2780990600586, 362.097412109375],\n", "    [73.66680145263672,\n", "     184.89895629882812,\n", "     83.82938385009766,\n", "     194.76913452148438],\n", "    [81.796875, 254.64093017578125, 92.2989501953125, 264.899169921875],\n", "    [22.146596908569336,\n", "     283.5763244628906,\n", "     34.050682067871094,\n", "     295.0987243652344],\n", "    [-0.867070198059082,\n", "     245.43423461914062,\n", "     9.767614364624023,\n", "     258.19219970703125],\n", "    [-0.5040903091430664,\n", "     300.3693542480469,\n", "     11.667229652404785,\n", "     314.8773498535156],\n", "    [33.08206558227539,\n", "     200.75204467773438,\n", "     44.23294448852539,\n", "     211.48721313476562],\n", "    [0.41797542572021484,\n", "     235.8883514404297,\n", "     12.271791458129883,\n", "     249.6859588623047],\n", "    [311.93060302734375,\n", "     118.64408111572266,\n", "     323.4385986328125,\n", "     129.98561096191406],\n", "    [284.964111328125,\n", "     185.5118865966797,\n", "     295.43634033203125,\n", "     195.7413787841797],\n", "    [196.3124237060547,\n", "     285.8703918457031,\n", "     206.5283966064453,\n", "     295.7273864746094],\n", "    [42.414634704589844,\n", "     317.74151611328125,\n", "     54.05670928955078,\n", "     329.44842529296875],\n", "    [87.88032531738281,\n", "     194.74771118164062,\n", "     98.11653137207031,\n", "     204.98880004882812],\n", "    [247.82595825195312,\n", "     146.77114868164062,\n", "     260.4662780761719,\n", "     159.02755737304688],\n", "    [216.6947479248047,\n", "     119.45423889160156,\n", "     228.36094665527344,\n", "     130.7049560546875],\n", "    [48.89011001586914,\n", "     248.75979614257812,\n", "     58.941898345947266,\n", "     258.3826599121094],\n", "    [152.6075439453125,\n", "     73.14053344726562,\n", "     163.73651123046875,\n", "     83.75430297851562],\n", "    [313.4577941894531,\n", "     110.36488342285156,\n", "     325.5320739746094,\n", "     122.29627990722656],\n", "    [264.8645324707031, 334.674560546875, 275.7064514160156, 345.697265625],\n", "    [180.07386779785156,\n", "     47.42152786254883,\n", "     191.1397247314453,\n", "     58.28518295288086],\n", "    [178.3466796875, 198.9862060546875, 188.403564453125, 208.52276611328125],\n", "    [257.28424072265625,\n", "     312.2425537109375,\n", "     267.635498046875,\n", "     322.25604248046875],\n", "    [121.94929504394531, 239.14599609375, 132.5478057861328, 249.457275390625],\n", "    [294.77996826171875,\n", "     262.24615478515625,\n", "     305.84442138671875,\n", "     273.31451416015625],\n", "    [49.51945877075195,\n", "     5.851042747497559,\n", "     62.08145523071289,\n", "     18.603538513183594],\n", "    [82.71521759033203, 71.4766845703125, 94.4551010131836, 83.3057861328125],\n", "    [3.4551992416381836,\n", "     127.94624328613281,\n", "     18.025169372558594,\n", "     142.6133270263672],\n", "    [244.17041015625,\n", "     117.89303588867188,\n", "     254.04840087890625,\n", "     127.561767578125],\n", "    [334.35870361328125,\n", "     100.05811309814453,\n", "     346.031005859375,\n", "     111.4709243774414],\n", "    [9.543363571166992,\n", "     21.95109748840332,\n", "     21.27552604675293,\n", "     33.69943618774414],\n", "    [110.8432388305664,\n", "     358.4842224121094,\n", "     123.56000518798828,\n", "     371.5653381347656],\n", "    [13.939743041992188,\n", "     207.0276641845703,\n", "     24.836883544921875,\n", "     217.83750915527344],\n", "    [129.2285919189453,\n", "     32.83285903930664,\n", "     139.9052276611328,\n", "     43.28738784790039],\n", "    [32.265010833740234,\n", "     150.615478515625,\n", "     43.80305862426758,\n", "     161.88262939453125],\n", "    [254.48963928222656,\n", "     132.70352172851562,\n", "     264.5145263671875,\n", "     142.53741455078125],\n", "    [122.29692840576172,\n", "     198.7108154296875,\n", "     133.32357788085938,\n", "     209.36801147460938],\n", "    [11.665685653686523,\n", "     16.282062530517578,\n", "     22.737722396850586,\n", "     27.47439193725586],\n", "    [162.005615234375, 132.11474609375, 173.2396240234375, 143.2664794921875],\n", "    [43.4259033203125,\n", "     132.28042602539062,\n", "     54.14036560058594,\n", "     142.30496215820312],\n", "    [7.025951385498047,\n", "     353.7175598144531,\n", "     19.063732147216797,\n", "     365.3869934082031],\n", "    [283.531982421875,\n", "     140.3773956298828,\n", "     293.69183349609375,\n", "     150.4207000732422],\n", "    [330.7984313964844,\n", "     61.939456939697266,\n", "     343.1788024902344,\n", "     74.28097534179688],\n", "    [90.8293228149414,\n", "     277.67474365234375,\n", "     101.48485565185547,\n", "     288.11566162109375],\n", "    [45.126609802246094,\n", "     166.7126007080078,\n", "     55.91712188720703,\n", "     177.33641052246094],\n", "    [-2.1249070167541504,\n", "     69.39559936523438,\n", "     8.50825309753418,\n", "     82.27668762207031],\n", "    [207.32150268554688,\n", "     337.8069763183594,\n", "     218.24453735351562,\n", "     348.7944641113281],\n", "    [0.8464565277099609,\n", "     141.28688049316406,\n", "     12.908571243286133,\n", "     155.4187774658203],\n", "    [317.1787414550781,\n", "     46.274593353271484,\n", "     327.8867492675781,\n", "     57.230525970458984],\n", "    [204.57809448242188,\n", "     229.64877319335938,\n", "     214.336181640625,\n", "     238.825439453125],\n", "    [97.38628387451172,\n", "     116.80278015136719,\n", "     108.76836395263672,\n", "     127.90164184570312],\n", "    [209.25540161132812,\n", "     6.514808177947998,\n", "     220.59805297851562,\n", "     17.6585750579834],\n", "    [182.37791442871094,\n", "     121.12560272216797,\n", "     194.7042999267578,\n", "     133.0703887939453],\n", "    [232.4949188232422,\n", "     183.86358642578125,\n", "     242.88877868652344,\n", "     194.00247192382812],\n", "    [97.67942810058594,\n", "     204.62698364257812,\n", "     109.61819458007812,\n", "     216.08895874023438],\n", "    [26.518373489379883, 261.535400390625, 37.43917465209961, 272.375],\n", "    [48.66399383544922,\n", "     159.7355499267578,\n", "     59.680694580078125,\n", "     170.26722717285156],\n", "    [131.08424377441406,\n", "     31.794677734375,\n", "     142.2893829345703,\n", "     42.918861389160156],\n", "    [129.8325958251953,\n", "     38.728939056396484,\n", "     140.3015899658203,\n", "     49.21384811401367],\n", "    [1.9333815574645996,\n", "     0.8584656715393066,\n", "     15.000160217285156,\n", "     10.412364959716797],\n", "    [224.25466918945312,\n", "     22.487638473510742,\n", "     236.04339599609375,\n", "     33.962554931640625],\n", "    [301.4657897949219,\n", "     55.03446960449219,\n", "     311.6443176269531,\n", "     64.71239471435547],\n", "    [277.2315368652344,\n", "     126.77957916259766,\n", "     287.2825012207031,\n", "     136.48231506347656],\n", "    [105.15447998046875,\n", "     288.291015625,\n", "     115.80357360839844,\n", "     298.83258056640625],\n", "    [221.48399353027344,\n", "     141.29104614257812,\n", "     232.9235382080078,\n", "     152.4976806640625],\n", "    [218.759765625, 88.46563720703125, 228.71405029296875, 97.57444763183594],\n", "    [324.6518859863281,\n", "     29.779285430908203,\n", "     337.1349182128906,\n", "     42.79300308227539],\n", "    [6.7172532081604,\n", "     80.38211059570312,\n", "     19.882688522338867,\n", "     93.45504760742188],\n", "    [72.2822036743164,\n", "     368.206298828125,\n", "     84.54967498779297,\n", "     380.67083740234375],\n", "    [165.08543395996094,\n", "     344.4503173828125,\n", "     177.3555450439453,\n", "     356.69012451171875],\n", "    [51.77001190185547,\n", "     158.87234497070312,\n", "     62.376365661621094,\n", "     169.01132202148438],\n", "    [51.28003692626953,\n", "     102.32664489746094,\n", "     61.928131103515625,\n", "     113.03602600097656],\n", "    [243.88357543945312,\n", "     205.6441650390625,\n", "     254.94342041015625,\n", "     216.47232055664062],\n", "    [269.3590393066406,\n", "     14.348825454711914,\n", "     279.9859924316406,\n", "     24.378896713256836],\n", "    [156.86212158203125, 372.67578125, 171.622314453125, 384.080810546875],\n", "    [304.24102783203125,\n", "     79.51205444335938,\n", "     316.6392822265625,\n", "     91.80072021484375],\n", "    [184.41897583007812, 265.5906066894531, 194.5126953125, 275.2759704589844],\n", "    [304.3854064941406,\n", "     40.45881271362305,\n", "     314.7825622558594,\n", "     51.00334548950195],\n", "    [287.3491516113281,\n", "     0.8885011672973633,\n", "     299.6623840332031,\n", "     10.135760307312012],\n", "    [178.5235595703125,\n", "     330.97662353515625,\n", "     189.88919067382812,\n", "     341.95025634765625],\n", "    [133.53013610839844,\n", "     330.1134033203125,\n", "     145.53163146972656,\n", "     342.04193115234375],\n", "    [137.87918090820312,\n", "     60.218971252441406,\n", "     148.83963012695312,\n", "     70.68730926513672],\n", "    [307.26727294921875,\n", "     170.94679260253906,\n", "     321.03466796875,\n", "     183.55848693847656],\n", "    [212.26644897460938,\n", "     240.8913116455078,\n", "     222.79205322265625,\n", "     251.3677215576172],\n", "    [54.276710510253906,\n", "     372.0018005371094,\n", "     68.2060775756836,\n", "     383.5591125488281],\n", "    [300.9291076660156,\n", "     190.08084106445312,\n", "     312.7547302246094,\n", "     202.36843872070312],\n", "    [184.9588165283203,\n", "     167.88299560546875,\n", "     195.59007263183594,\n", "     178.04791259765625],\n", "    [209.42800903320312,\n", "     57.15709686279297,\n", "     221.33779907226562,\n", "     68.67882537841797],\n", "    [271.6397399902344,\n", "     231.4233856201172,\n", "     281.8354797363281,\n", "     241.51353454589844],\n", "    [190.36773681640625,\n", "     184.4508056640625,\n", "     200.03662109375,\n", "     193.64456176757812],\n", "    [152.10452270507812,\n", "     262.4323425292969,\n", "     163.14306640625,\n", "     273.1617736816406],\n", "    [86.77901458740234,\n", "     371.9412841796875,\n", "     100.6327133178711,\n", "     383.64129638671875],\n", "    [123.56963348388672,\n", "     149.29150390625,\n", "     133.82325744628906,\n", "     159.33584594726562],\n", "    [183.20082092285156,\n", "     80.37374114990234,\n", "     193.7209014892578,\n", "     90.43270111083984],\n", "    [178.7703399658203,\n", "     372.2239685058594,\n", "     192.94834899902344,\n", "     383.7501525878906],\n", "    [108.37237548828125, 286.5517578125, 119.29791259765625, 297.203857421875],\n", "    [123.73908996582031,\n", "     370.8573913574219,\n", "     137.7522430419922,\n", "     383.1370544433594],\n", "    [35.11634826660156,\n", "     148.9812469482422,\n", "     46.14403533935547,\n", "     159.66844177246094],\n", "    [1.5965070724487305,\n", "     370.51263427734375,\n", "     16.59478759765625,\n", "     382.4713134765625],\n", "    [59.30940246582031,\n", "     182.85858154296875,\n", "     70.05914306640625,\n", "     193.13668823242188],\n", "    [180.3502197265625,\n", "     365.6119689941406,\n", "     192.4090576171875,\n", "     378.2360534667969],\n", "    [176.15440368652344,\n", "     144.5588836669922,\n", "     186.97364807128906,\n", "     155.1918182373047],\n", "    [370.5424499511719,\n", "     0.23828601837158203,\n", "     384.6661071777344,\n", "     13.781608581542969],\n", "    [83.98289489746094,\n", "     158.68312072753906,\n", "     95.12034606933594,\n", "     169.6947784423828],\n", "    [240.03150939941406,\n", "     120.01185607910156,\n", "     250.67442321777344,\n", "     130.26315307617188],\n", "    [107.16919708251953,\n", "     247.3773651123047,\n", "     117.12293243408203,\n", "     256.4576110839844],\n", "    [116.5501708984375,\n", "     322.85101318359375,\n", "     128.22901916503906,\n", "     334.3031005859375],\n", "    [259.5148620605469,\n", "     263.2215881347656,\n", "     271.0800476074219,\n", "     275.0038757324219],\n", "    [260.0535583496094,\n", "     310.2010803222656,\n", "     269.8138122558594,\n", "     319.2693786621094],\n", "    [165.23861694335938,\n", "     37.34820556640625,\n", "     176.664306640625,\n", "     49.050376892089844],\n", "    [99.3550033569336,\n", "     29.175350189208984,\n", "     109.76065826416016,\n", "     39.07156753540039],\n", "    [268.963134765625,\n", "     48.785919189453125,\n", "     279.26995849609375,\n", "     59.007423400878906],\n", "    [263.45111083984375,\n", "     15.965644836425781,\n", "     274.59326171875,\n", "     26.40772247314453],\n", "    [316.0600891113281,\n", "     54.551490783691406,\n", "     326.4823303222656,\n", "     64.98461151123047],\n", "    [139.28958129882812,\n", "     252.78289794921875,\n", "     150.34811401367188,\n", "     263.112548828125],\n", "    [90.72315979003906,\n", "     239.3654022216797,\n", "     101.68614196777344,\n", "     249.2449188232422],\n", "    [15.423250198364258,\n", "     198.69483947753906,\n", "     26.679189682006836,\n", "     209.9476776123047],\n", "    [275.4080505371094,\n", "     220.93252563476562,\n", "     286.1823425292969,\n", "     231.65005493164062],\n", "    [244.844970703125,\n", "     290.44708251953125,\n", "     255.96768188476562,\n", "     301.26708984375],\n", "    [48.62179946899414,\n", "     0.4920167922973633,\n", "     63.61086654663086,\n", "     11.552157402038574],\n", "    [41.60829544067383,\n", "     174.8984832763672,\n", "     53.14438247680664,\n", "     185.78526306152344],\n", "    [36.22852325439453,\n", "     239.38381958007812,\n", "     46.45689392089844,\n", "     249.03167724609375],\n", "    [291.8982849121094,\n", "     31.7963924407959,\n", "     302.2743835449219,\n", "     41.732872009277344],\n", "    [75.98960876464844,\n", "     47.60487365722656,\n", "     86.59486389160156,\n", "     57.67449951171875],\n", "    [202.0154571533203,\n", "     173.12069702148438,\n", "     212.6418914794922,\n", "     183.30764770507812],\n", "    [231.355224609375,\n", "     141.96656799316406,\n", "     242.6766357421875,\n", "     152.3262176513672],\n", "    [18.85610008239746, 325.505859375, 29.609140396118164, 335.77081298828125],\n", "    [97.30033874511719,\n", "     31.548189163208008,\n", "     108.02386474609375,\n", "     41.8584098815918],\n", "    [228.68441772460938,\n", "     196.74847412109375,\n", "     238.86474609375,\n", "     206.88739013671875],\n", "    [317.1436462402344,\n", "     206.21517944335938,\n", "     329.1694641113281,\n", "     218.68173217773438],\n", "    [112.16258239746094,\n", "     302.8025817871094,\n", "     122.41011047363281,\n", "     312.4493713378906],\n", "    [40.500789642333984,\n", "     87.01835632324219,\n", "     51.5018196105957,\n", "     97.90573120117188],\n", "    [293.4486999511719,\n", "     161.99513244628906,\n", "     305.6208190917969,\n", "     174.7991180419922],\n", "    [148.2948760986328,\n", "     365.8362731933594,\n", "     161.0458526611328,\n", "     378.5329895019531],\n", "    [225.38131713867188,\n", "     110.51710510253906,\n", "     235.70391845703125,\n", "     120.57093811035156],\n", "    [228.06695556640625,\n", "     70.946044921875,\n", "     238.74685668945312,\n", "     81.06459045410156],\n", "    [215.1057891845703,\n", "     364.4637145996094,\n", "     230.0236053466797,\n", "     379.3763732910156],\n", "    [43.39013671875, 314.22869873046875, 54.35491943359375, 325.438720703125],\n", "    [202.72265625, 167.58477783203125, 213.35977172851562, 177.97186279296875],\n", "    [111.70855712890625,\n", "     176.89706420898438,\n", "     122.78767395019531,\n", "     187.75717163085938],\n", "    [180.8780059814453,\n", "     136.46937561035156,\n", "     191.57432556152344,\n", "     147.00367736816406],\n", "    [218.53648376464844,\n", "     179.73489379882812,\n", "     229.8761749267578,\n", "     190.582275390625],\n", "    [173.26519775390625,\n", "     347.4642028808594,\n", "     186.48626708984375,\n", "     360.2859191894531],\n", "    [242.37265014648438,\n", "     215.75167846679688,\n", "     252.95645141601562,\n", "     225.8797607421875],\n", "    [92.92976379394531,\n", "     312.7206726074219,\n", "     106.53533935546875,\n", "     326.0395812988281],\n", "    [185.597900390625,\n", "     15.766792297363281,\n", "     198.58096313476562,\n", "     28.72707748413086],\n", "    [192.0989990234375,\n", "     87.5135498046875,\n", "     202.37741088867188,\n", "     97.36927795410156],\n", "    [339.2874755859375,\n", "     44.43239212036133,\n", "     351.0679931640625,\n", "     55.9860954284668],\n", "    [281.3403015136719,\n", "     113.19962310791016,\n", "     291.5782775878906,\n", "     123.36243438720703],\n", "    [112.16747283935547,\n", "     272.4594421386719,\n", "     122.2436294555664,\n", "     281.9770202636719],\n", "    [225.64500427246094,\n", "     250.10714721679688,\n", "     237.2382354736328,\n", "     261.4654235839844],\n", "    [171.9162139892578,\n", "     10.095891952514648,\n", "     183.7523956298828,\n", "     22.221887588500977],\n", "    [320.7087707519531,\n", "     38.957183837890625,\n", "     332.0588684082031,\n", "     50.20851135253906],\n", "    [202.62387084960938,\n", "     86.20063781738281,\n", "     212.99819946289062,\n", "     95.81637573242188],\n", "    [211.1625518798828,\n", "     152.8224639892578,\n", "     221.9821014404297,\n", "     163.37037658691406],\n", "    [188.70545959472656,\n", "     68.13896179199219,\n", "     199.46800231933594,\n", "     78.7208251953125],\n", "    [10.56313419342041,\n", "     41.90315628051758,\n", "     23.001346588134766,\n", "     54.443546295166016],\n", "    [341.7178649902344,\n", "     87.31027221679688,\n", "     353.0026550292969,\n", "     99.0721435546875],\n", "    [42.00503158569336,\n", "     29.9252986907959,\n", "     53.80624771118164,\n", "     41.73955535888672],\n", "    [33.390193939208984,\n", "     48.79853820800781,\n", "     48.291812896728516,\n", "     63.00718688964844],\n", "    [202.4994354248047, 312.8115234375, 212.4674835205078, 322.75640869140625],\n", "    [279.02789306640625,\n", "     64.36659240722656,\n", "     289.97845458984375,\n", "     75.1278076171875],\n", "    [138.26490783691406,\n", "     152.80335998535156,\n", "     149.0006866455078,\n", "     163.27146911621094],\n", "    [194.79042053222656,\n", "     198.85032653808594,\n", "     204.85789489746094,\n", "     208.7928009033203],\n", "    [138.17469787597656,\n", "     295.14947509765625,\n", "     149.01051330566406,\n", "     305.8369140625],\n", "    [230.353515625,\n", "     120.37235260009766,\n", "     240.64053344726562,\n", "     130.42681884765625],\n", "    [350.5372314453125,\n", "     63.711185455322266,\n", "     361.0972900390625,\n", "     74.4061050415039],\n", "    [73.62297821044922,\n", "     39.46769332885742,\n", "     84.13574981689453,\n", "     49.71867752075195],\n", "    [240.93930053710938,\n", "     112.24686431884766,\n", "     251.4031982421875,\n", "     122.25434112548828],\n", "    [51.66770553588867,\n", "     247.50399780273438,\n", "     61.455074310302734,\n", "     256.9169616699219],\n", "    [235.35113525390625,\n", "     237.4933319091797,\n", "     245.64303588867188,\n", "     247.1327362060547],\n", "    [171.68875122070312,\n", "     309.2213134765625,\n", "     181.67886352539062,\n", "     318.94818115234375],\n", "    [196.08978271484375,\n", "     14.720057487487793,\n", "     207.392578125,\n", "     25.484039306640625],\n", "    [200.5452880859375,\n", "     28.644023895263672,\n", "     211.85385131835938,\n", "     39.31795120239258],\n", "    [242.2220001220703, 295.07421875, 253.5199432373047, 306.02789306640625],\n", "    [305.2600402832031,\n", "     33.18923568725586,\n", "     315.4779968261719,\n", "     43.28178787231445],\n", "    [96.66429901123047,\n", "     111.4168701171875,\n", "     110.70159149169922,\n", "     124.69244384765625],\n", "    [31.375564575195312,\n", "     96.5968246459961,\n", "     45.37164306640625,\n", "     109.95758819580078],\n", "    [-0.809471607208252,\n", "     195.9994354248047,\n", "     10.422567367553711,\n", "     209.46543884277344],\n", "    [227.01998901367188,\n", "     251.2560272216797,\n", "     239.51895141601562,\n", "     263.59991455078125],\n", "    [171.1913299560547,\n", "     115.5644760131836,\n", "     182.81285095214844,\n", "     127.53511810302734],\n", "    [65.96125030517578,\n", "     24.111547470092773,\n", "     77.07157135009766,\n", "     35.1466064453125],\n", "    [259.65142822265625,\n", "     30.383886337280273,\n", "     269.8330078125,\n", "     40.002384185791016],\n", "    [80.89291381835938,\n", "     53.680198669433594,\n", "     92.31715393066406,\n", "     64.489501953125],\n", "    [124.8770751953125,\n", "     51.547420501708984,\n", "     135.48312377929688,\n", "     62.09702682495117],\n", "    [292.3166198730469,\n", "     0.7058887481689453,\n", "     304.9189758300781,\n", "     9.744170188903809],\n", "    [272.9454040527344,\n", "     34.749839782714844,\n", "     282.7827453613281,\n", "     44.329627990722656],\n", "    [87.92573547363281,\n", "     101.54468536376953,\n", "     98.49855041503906,\n", "     111.95682525634766],\n", "    [303.404541015625,\n", "     0.7096309661865234,\n", "     315.4049072265625,\n", "     9.58244514465332],\n", "    [144.76190185546875,\n", "     31.16828155517578,\n", "     158.1337890625,\n", "     44.104576110839844],\n", "    [226.39419555664062,\n", "     19.563682556152344,\n", "     237.52651977539062,\n", "     30.394054412841797],\n", "    [218.86151123046875,\n", "     31.716306686401367,\n", "     231.03955078125,\n", "     44.1944580078125],\n", "    [143.3287811279297,\n", "     369.7131042480469,\n", "     155.9982147216797,\n", "     382.5664367675781],\n", "    [202.32846069335938,\n", "     361.42816162109375,\n", "     215.5650634765625,\n", "     374.54302978515625],\n", "    [73.9735107421875,\n", "     271.4914855957031,\n", "     84.83795166015625,\n", "     282.1869201660156],\n", "    [134.76551818847656,\n", "     117.79764556884766,\n", "     144.66831970214844,\n", "     127.6885757446289],\n", "    [41.53980255126953,\n", "     132.8330841064453,\n", "     52.812294006347656,\n", "     143.8894805908203],\n", "    [309.759521484375,\n", "     0.1064763069152832,\n", "     322.7830810546875,\n", "     9.410202026367188],\n", "    [51.4364013671875,\n", "     296.3847961425781,\n", "     63.795867919921875,\n", "     308.4128723144531],\n", "    [258.53802490234375,\n", "     118.72322845458984,\n", "     268.3768310546875,\n", "     128.33840942382812],\n", "    [201.87478637695312,\n", "     72.39897155761719,\n", "     213.22366333007812,\n", "     84.02665710449219],\n", "    [362.0705261230469,\n", "     37.29507827758789,\n", "     374.0402526855469,\n", "     49.417423248291016],\n", "    [140.50474548339844,\n", "     205.41526794433594,\n", "     150.72239685058594,\n", "     214.98643493652344],\n", "    [29.419273376464844,\n", "     73.89547729492188,\n", "     41.351654052734375,\n", "     85.60078430175781],\n", "    [68.36620330810547,\n", "     246.218017578125,\n", "     78.62354278564453,\n", "     256.3346252441406],\n", "    [210.59776306152344,\n", "     286.1058044433594,\n", "     220.91905212402344,\n", "     296.4501037597656],\n", "    [97.25202941894531,\n", "     344.6275939941406,\n", "     109.25135803222656,\n", "     356.3553161621094],\n", "    [164.9432830810547,\n", "     92.97810363769531,\n", "     175.36448669433594,\n", "     103.22987365722656],\n", "    [81.2531509399414,\n", "     17.11684799194336,\n", "     93.77339935302734,\n", "     29.005889892578125],\n", "    [225.54444885253906,\n", "     305.4070739746094,\n", "     235.4895477294922,\n", "     314.6484069824219],\n", "    [261.32977294921875,\n", "     128.66053771972656,\n", "     271.43292236328125,\n", "     138.14170837402344],\n", "    [130.92759704589844,\n", "     276.6998291015625,\n", "     141.47337341308594,\n", "     287.06719970703125],\n", "    [19.482009887695312,\n", "     5.715690612792969,\n", "     30.388824462890625,\n", "     16.41864013671875],\n", "    [97.59486389160156,\n", "     258.3783264160156,\n", "     109.88412475585938,\n", "     270.1602478027344],\n", "    [49.89140319824219, 95.8569107055664, 63.817138671875, 109.17957305908203],\n", "    [295.44500732421875,\n", "     120.17547607421875,\n", "     305.2996826171875,\n", "     129.5030975341797],\n", "    [211.49346923828125,\n", "     145.4901885986328,\n", "     223.42706298828125,\n", "     156.7413787841797],\n", "    [-1.6670770645141602,\n", "     99.72134399414062,\n", "     9.915301322937012,\n", "     113.72860717773438],\n", "    [246.98707580566406,\n", "     254.95652770996094,\n", "     257.4554443359375,\n", "     265.29290771484375],\n", "    [254.86851501464844, 81.9913558959961, 265.94580078125, 93.39746856689453],\n", "    [96.70780944824219,\n", "     168.51820373535156,\n", "     107.47669982910156,\n", "     179.2686309814453],\n", "    [-2.629255771636963,\n", "     338.6871337890625,\n", "     7.76597261428833,\n", "     351.33074951171875],\n", "    [333.3764953613281,\n", "     4.278570175170898,\n", "     345.4556579589844,\n", "     16.5123348236084],\n", "    [289.85601806640625,\n", "     232.43295288085938,\n", "     301.25030517578125,\n", "     243.6920166015625],\n", "    [21.321149826049805,\n", "     335.5896911621094,\n", "     31.777238845825195,\n", "     345.2380065917969],\n", "    [241.83082580566406,\n", "     167.39846801757812,\n", "     252.13597106933594,\n", "     177.4224853515625],\n", "    [239.5205078125, 32.49181365966797, 250.773681640625, 43.74993133544922],\n", "    [16.335769653320312,\n", "     247.4996795654297,\n", "     26.703269958496094,\n", "     257.95916748046875],\n", "    [266.7169189453125,\n", "     247.84408569335938,\n", "     277.51861572265625,\n", "     258.50189208984375],\n", "    [146.32688903808594,\n", "     352.76019287109375,\n", "     158.94834899902344,\n", "     364.62750244140625],\n", "    [83.79125213623047,\n", "     120.04862213134766,\n", "     93.5694808959961,\n", "     129.29795837402344],\n", "    [163.62863159179688,\n", "     287.6842346191406,\n", "     173.83792114257812,\n", "     297.0834045410156],\n", "    [12.908893585205078,\n", "     212.99334716796875,\n", "     23.400222778320312,\n", "     223.48492431640625],\n", "    [1.4725627899169922,\n", "     309.06097412109375,\n", "     13.957618713378906,\n", "     323.25897216796875],\n", "    [271.8839111328125,\n", "     177.45895385742188,\n", "     282.62750244140625,\n", "     188.03274536132812],\n", "    [122.97488403320312,\n", "     246.0135498046875,\n", "     133.67263793945312,\n", "     256.1246032714844],\n", "    [180.2664337158203,\n", "     95.21118927001953,\n", "     189.79188537597656,\n", "     104.90782928466797],\n", "    [306.8360290527344, 135.681640625, 318.0704040527344, 146.58407592773438],\n", "    [301.56866455078125,\n", "     142.3512725830078,\n", "     313.36566162109375,\n", "     154.5946807861328],\n", "    [161.187255859375,\n", "     237.36619567871094,\n", "     171.92837524414062,\n", "     247.7888946533203],\n", "    [284.3370666503906,\n", "     282.5493469238281,\n", "     296.9435729980469,\n", "     295.2147521972656],\n", "    [168.040283203125,\n", "     114.42021942138672,\n", "     180.162841796875,\n", "     126.2984390258789],\n", "    [222.02711486816406,\n", "     319.3043212890625,\n", "     231.71580505371094,\n", "     328.86932373046875],\n", "    [9.591980934143066,\n", "     302.1847839355469,\n", "     21.178871154785156,\n", "     313.4090881347656],\n", "    [112.9898681640625,\n", "     70.08692169189453,\n", "     124.03805541992188,\n", "     80.68659210205078],\n", "    [193.88128662109375,\n", "     337.5185852050781,\n", "     204.83499145507812,\n", "     348.4949035644531],\n", "    [113.68820190429688,\n", "     161.06614685058594,\n", "     126.22740173339844,\n", "     172.85987854003906],\n", "    [249.89801025390625,\n", "     185.8028106689453,\n", "     260.9308166503906,\n", "     196.60084533691406],\n", "    [2.8953065872192383,\n", "     90.96382904052734,\n", "     17.135082244873047,\n", "     105.10010528564453],\n", "    [171.77076721191406,\n", "     344.8531494140625,\n", "     184.3184051513672,\n", "     357.46240234375],\n", "    [235.59742736816406,\n", "     94.18968200683594,\n", "     245.08460998535156,\n", "     103.01023864746094],\n", "    [73.57344055175781,\n", "     366.1274108886719,\n", "     85.64179992675781,\n", "     378.0409851074219],\n", "    [136.42758178710938, 288.94140625, 146.78463745117188, 299.24688720703125],\n", "    [173.41725158691406,\n", "     78.63341522216797,\n", "     183.99794006347656,\n", "     88.78917694091797],\n", "    [354.94677734375,\n", "     14.882364273071289,\n", "     367.18511962890625,\n", "     27.1080379486084],\n", "    [189.5721435546875,\n", "     80.46744537353516,\n", "     199.9283447265625,\n", "     90.1987533569336],\n", "    [210.02078247070312,\n", "     193.58343505859375,\n", "     221.65985107421875,\n", "     204.7147216796875],\n", "    [250.8314208984375, 323.7554931640625, 261.71875, 334.75823974609375],\n", "    [353.0196838378906,\n", "     49.020233154296875,\n", "     366.6365661621094,\n", "     62.09358215332031],\n", "    [154.10934448242188,\n", "     154.17344665527344,\n", "     165.03042602539062,\n", "     165.12351989746094],\n", "    [357.4624328613281,\n", "     -1.5983498096466064,\n", "     370.4193420410156,\n", "     8.53071117401123],\n", "    [210.16004943847656,\n", "     155.80401611328125,\n", "     221.2326202392578,\n", "     166.45867919921875],\n", "    [330.66943359375,\n", "     119.70345306396484,\n", "     341.81488037109375,\n", "     130.4757843017578],\n", "    [71.57830810546875,\n", "     360.9364318847656,\n", "     82.72329711914062,\n", "     371.7979431152344],\n", "    [1.9778404235839844,\n", "     324.9566955566406,\n", "     13.86622142791748,\n", "     335.9220275878906],\n", "    [121.0123291015625,\n", "     292.9327392578125,\n", "     131.741455078125,\n", "     303.6146240234375],\n", "    [336.27728271484375,\n", "     96.76777648925781,\n", "     348.3883056640625,\n", "     108.66258239746094],\n", "    [147.5708465576172,\n", "     277.0632019042969,\n", "     158.1433868408203,\n", "     287.4352722167969],\n", "    [259.2625732421875,\n", "     208.49810791015625,\n", "     271.7745361328125,\n", "     220.400146484375],\n", "    [227.04635620117188,\n", "     62.11654281616211,\n", "     237.9759521484375,\n", "     72.83807373046875],\n", "    [36.98994445800781,\n", "     276.28253173828125,\n", "     47.89192199707031,\n", "     287.40716552734375],\n", "    [317.33563232421875,\n", "     145.73684692382812,\n", "     328.877685546875,\n", "     157.14468383789062],\n", "    [67.49600982666016,\n", "     156.9709014892578,\n", "     77.9876480102539,\n", "     167.40342712402344],\n", "    [320.2326965332031,\n", "     127.39198303222656,\n", "     334.7750549316406,\n", "     141.3638458251953],\n", "    [124.72660064697266,\n", "     233.9851837158203,\n", "     134.89447021484375,\n", "     244.2028045654297],\n", "    [113.67655944824219,\n", "     256.9395446777344,\n", "     126.46278381347656,\n", "     269.1950988769531],\n", "    [194.4394989013672,\n", "     193.69573974609375,\n", "     206.35789489746094,\n", "     205.01010131835938],\n", "    [129.6776580810547,\n", "     128.21409606933594,\n", "     143.29258728027344,\n", "     141.0831756591797],\n", "    [163.23590087890625,\n", "     229.67538452148438,\n", "     175.04617309570312,\n", "     241.15127563476562],\n", "    [33.485836029052734,\n", "     0.0803842544555664,\n", "     46.6849250793457,\n", "     9.436758995056152],\n", "    [283.396728515625,\n", "     13.6194429397583,\n", "     294.49432373046875,\n", "     24.523231506347656],\n", "    [164.62876892089844,\n", "     366.5240783691406,\n", "     176.4246368408203,\n", "     378.5299987792969],\n", "    [32.23149871826172,\n", "     144.2809600830078,\n", "     44.830284118652344,\n", "     156.3003387451172],\n", "    [133.3965606689453,\n", "     356.3763732910156,\n", "     144.19435119628906,\n", "     367.3396301269531],\n", "    [58.028778076171875,\n", "     269.3597412109375,\n", "     68.87303161621094,\n", "     280.06024169921875],\n", "    [13.632360458374023,\n", "     259.4928894042969,\n", "     24.182458877563477,\n", "     269.9454650878906],\n", "    [41.68637466430664,\n", "     265.8785705566406,\n", "     51.846370697021484,\n", "     276.1037292480469],\n", "    [152.6021270751953,\n", "     155.87338256835938,\n", "     163.52528381347656,\n", "     166.95977783203125],\n", "    [280.9057922363281, 141.34588623046875, 291.3668518066406, 151.1533203125],\n", "    [170.2805938720703,\n", "     305.3269348144531,\n", "     180.6156768798828,\n", "     315.3964538574219],\n", "    [161.4495391845703,\n", "     271.442138671875,\n", "     174.2369842529297,\n", "     283.52630615234375],\n", "    [126.70429229736328,\n", "     186.05764770507812,\n", "     137.32882690429688,\n", "     196.44406127929688],\n", "    [0.732391357421875,\n", "     31.787643432617188,\n", "     14.64787483215332,\n", "     46.159454345703125],\n", "    [101.53941345214844,\n", "     299.4189758300781,\n", "     112.708740234375,\n", "     310.0787658691406],\n", "    [303.6429443359375,\n", "     128.3296356201172,\n", "     317.4305419921875,\n", "     141.3693084716797],\n", "    [31.00979232788086,\n", "     53.01994705200195,\n", "     44.03323745727539,\n", "     65.4195556640625],\n", "    [65.7413330078125, 336.390869140625, 78.21604919433594, 348.1356201171875],\n", "    [265.53338623046875,\n", "     197.48817443847656,\n", "     275.76263427734375,\n", "     207.0966033935547],\n", "    [12.207382202148438,\n", "     104.45841217041016,\n", "     25.422142028808594,\n", "     117.36890411376953],\n", "    [117.82858276367188,\n", "     270.99468994140625,\n", "     127.72744750976562,\n", "     280.34490966796875],\n", "    [208.68492126464844,\n", "     0.42293357849121094,\n", "     222.2284393310547,\n", "     10.344366073608398],\n", "    [80.55509948730469,\n", "     121.41999816894531,\n", "     90.63002014160156,\n", "     131.1165771484375],\n", "    [258.1075439453125,\n", "     113.50180053710938,\n", "     269.418701171875,\n", "     124.41184997558594],\n", "    [296.31097412109375,\n", "     55.82521057128906,\n", "     306.58880615234375,\n", "     65.59740447998047],\n", "    [35.400394439697266,\n", "     336.09344482421875,\n", "     46.05204391479492,\n", "     346.465576171875],\n", "    [162.08164978027344,\n", "     80.76937866210938,\n", "     173.99269104003906,\n", "     92.13046264648438],\n", "    [234.6338653564453,\n", "     233.31057739257812,\n", "     245.0107879638672,\n", "     243.50628662109375],\n", "    [29.152759552001953,\n", "     127.70573425292969,\n", "     39.97726821899414,\n", "     138.4488983154297],\n", "    [55.14335250854492,\n", "     187.67755126953125,\n", "     65.91192626953125,\n", "     198.30581665039062],\n", "    [130.96739196777344,\n", "     305.7080383300781,\n", "     143.41615295410156,\n", "     317.7198791503906],\n", "    [289.393798828125, 223.90988159179688, 303.90667724609375, 237.5966796875],\n", "    [186.25616455078125,\n", "     122.90919494628906,\n", "     197.64312744140625,\n", "     134.03208923339844],\n", "    [224.703125, 311.6375732421875, 234.64556884765625, 321.34857177734375],\n", "    [86.47779846191406,\n", "     264.4297790527344,\n", "     96.80111694335938,\n", "     274.3359680175781],\n", "    [67.47050476074219,\n", "     341.2698059082031,\n", "     78.28944396972656,\n", "     351.6157531738281],\n", "    [211.77850341796875,\n", "     295.6336975097656,\n", "     221.62051391601562,\n", "     304.6466369628906],\n", "    [130.44158935546875,\n", "     76.24860382080078,\n", "     141.5279541015625,\n", "     86.9815444946289],\n", "    [1.5340189933776855,\n", "     273.6964416503906,\n", "     14.834590911865234,\n", "     286.3772277832031],\n", "    [249.7017059326172,\n", "     326.412841796875,\n", "     260.4266052246094,\n", "     336.94781494140625],\n", "    [148.56285095214844,\n", "     106.3329849243164,\n", "     160.4427947998047,\n", "     117.87554168701172],\n", "    [322.28076171875,\n", "     35.579830169677734,\n", "     334.32232666015625,\n", "     47.35881423950195],\n", "    [61.763694763183594,\n", "     229.6971435546875,\n", "     72.08165740966797,\n", "     239.86862182617188],\n", "    [32.83757781982422,\n", "     249.38026428222656,\n", "     43.562049865722656,\n", "     260.1400146484375],\n", "    [97.7491226196289,\n", "     23.495935440063477,\n", "     108.7073745727539,\n", "     34.289276123046875],\n", "    [88.91024780273438,\n", "     369.6816101074219,\n", "     100.45901489257812,\n", "     381.8374328613281],\n", "    [229.20089721679688,\n", "     371.1943664550781,\n", "     244.70108032226562,\n", "     384.0532531738281],\n", "    [174.50657653808594,\n", "     373.3740234375,\n", "     189.31605529785156,\n", "     384.47357177734375],\n", "    [17.242692947387695,\n", "     369.5058288574219,\n", "     31.099058151245117,\n", "     382.0720520019531],\n", "    [160.48931884765625,\n", "     273.2342834472656,\n", "     171.86007690429688,\n", "     284.3974304199219],\n", "    [174.6478729248047,\n", "     367.9659423828125,\n", "     191.0763702392578,\n", "     382.52789306640625],\n", "    [315.8114318847656,\n", "     148.286865234375,\n", "     327.5306701660156,\n", "     159.89730834960938],\n", "    [337.3939208984375,\n", "     47.70451736450195,\n", "     348.92449951171875,\n", "     58.91554641723633],\n", "    [39.371639251708984,\n", "     36.61090850830078,\n", "     49.883060455322266,\n", "     46.927215576171875],\n", "    [145.41262817382812,\n", "     318.6598205566406,\n", "     155.82369995117188,\n", "     328.6261291503906],\n", "    [15.150866508483887,\n", "     372.7167663574219,\n", "     29.84503936767578,\n", "     383.8720397949219],\n", "    [202.10302734375,\n", "     318.8916931152344,\n", "     212.53302001953125,\n", "     328.9434509277344],\n", "    [38.32396697998047,\n", "     119.10240936279297,\n", "     50.603675842285156,\n", "     131.0682373046875],\n", "    [112.802490234375,\n", "     212.81033325195312,\n", "     123.94500732421875,\n", "     223.32015991210938],\n", "    [89.20791625976562,\n", "     2.603778839111328,\n", "     101.44190979003906,\n", "     14.926355361938477],\n", "    [161.12615966796875,\n", "     130.2786102294922,\n", "     172.29391479492188,\n", "     141.4844512939453],\n", "    [109.4443359375, 149.71914672851562, 120.01708984375, 159.93563842773438],\n", "    [198.61508178710938,\n", "     159.8877410888672,\n", "     209.41754150390625,\n", "     170.23939514160156],\n", "    [139.1857452392578,\n", "     157.71827697753906,\n", "     149.4800262451172,\n", "     167.65550231933594],\n", "    [193.17587280273438,\n", "     288.4115905761719,\n", "     203.24566650390625,\n", "     298.2472839355469],\n", "    [82.90013122558594,\n", "     167.16641235351562,\n", "     93.60487365722656,\n", "     177.15548706054688],\n", "    [109.51497650146484,\n", "     86.59596252441406,\n", "     120.55457305908203,\n", "     96.80209350585938],\n", "    [162.2708740234375,\n", "     231.74105834960938,\n", "     173.74136352539062,\n", "     243.086181640625],\n", "    [297.1964111328125,\n", "     17.460723876953125,\n", "     308.0712890625,\n", "     28.214778900146484],\n", "    [161.79403686523438,\n", "     176.0164337158203,\n", "     174.20361328125,\n", "     187.8739776611328],\n", "    [64.76556396484375,\n", "     343.801025390625,\n", "     75.33609008789062,\n", "     354.16632080078125],\n", "    [302.04827880859375,\n", "     119.78480529785156,\n", "     312.61846923828125,\n", "     129.8864288330078],\n", "    [161.43606567382812,\n", "     277.35272216796875,\n", "     172.62783813476562,\n", "     288.4713134765625],\n", "    [99.26868438720703,\n", "     165.8245086669922,\n", "     109.8144302368164,\n", "     176.14356994628906],\n", "    [101.48167419433594,\n", "     84.45817565917969,\n", "     113.05499267578125,\n", "     95.37434387207031],\n", "    [194.48960876464844,\n", "     242.11746215820312,\n", "     204.06455993652344,\n", "     251.56796264648438],\n", "    [304.05364990234375, 169.48828125, 317.14215087890625, 182.4903564453125],\n", "    [76.52507019042969,\n", "     306.02056884765625,\n", "     88.67359924316406,\n", "     318.1480712890625],\n", "    [212.33839416503906,\n", "     206.1309356689453,\n", "     222.1090850830078,\n", "     215.26304626464844],\n", "    [294.4379577636719,\n", "     97.21022033691406,\n", "     305.3486633300781,\n", "     107.83219909667969],\n", "    [3.5906615257263184,\n", "     32.07762145996094,\n", "     16.25673484802246,\n", "     44.77289581298828],\n", "    [121.16106414794922,\n", "     151.97128295898438,\n", "     132.34335327148438,\n", "     163.00039672851562],\n", "    [196.82252502441406,\n", "     340.0743713378906,\n", "     207.67018127441406,\n", "     350.3646545410156],\n", "    [324.346923828125,\n", "     131.8555450439453,\n", "     334.73052978515625,\n", "     142.2403106689453],\n", "    [70.5562744140625,\n", "     195.5343017578125,\n", "     81.06710815429688,\n", "     206.14450073242188],\n", "    [172.1217498779297,\n", "     256.38531494140625,\n", "     182.0684356689453,\n", "     265.93280029296875],\n", "    [51.259761810302734,\n", "     334.5321960449219,\n", "     62.30759048461914,\n", "     345.4532165527344],\n", "    [250.10536193847656,\n", "     95.62289428710938,\n", "     260.7427978515625,\n", "     105.69300842285156],\n", "    [122.15481567382812,\n", "     291.5124816894531,\n", "     133.0916290283203,\n", "     301.9169006347656],\n", "    [139.02427673339844,\n", "     330.1849060058594,\n", "     150.57191467285156,\n", "     341.9275207519531],\n", "    [113.52678680419922,\n", "     125.35173797607422,\n", "     124.9051742553711,\n", "     136.01580810546875],\n", "    [241.40338134765625,\n", "     288.0808410644531,\n", "     252.99667358398438,\n", "     299.3190002441406],\n", "    [32.91374206542969,\n", "     368.5809631347656,\n", "     47.32032012939453,\n", "     381.6097717285156],\n", "    [192.38331604003906,\n", "     340.0165710449219,\n", "     203.90025329589844,\n", "     351.3562316894531],\n", "    [116.49205017089844,\n", "     90.1331558227539,\n", "     127.228271484375,\n", "     100.7351303100586],\n", "    [275.56884765625,\n", "     32.44337844848633,\n", "     285.84332275390625,\n", "     42.28408432006836],\n", "    [65.89306640625,\n", "     143.93421936035156,\n", "     78.19467163085938,\n", "     155.70960998535156],\n", "    [225.53860473632812,\n", "     295.98834228515625,\n", "     235.20706176757812,\n", "     305.013671875],\n", "    [33.454410552978516,\n", "     240.0835723876953,\n", "     43.553401947021484,\n", "     249.9808807373047],\n", "    [164.3325653076172,\n", "     324.7784118652344,\n", "     174.5904998779297,\n", "     334.7123718261719],\n", "    [114.13590240478516,\n", "     209.26727294921875,\n", "     127.42183685302734,\n", "     222.06814575195312],\n", "    [33.51365661621094,\n", "     191.28211975097656,\n", "     46.97663879394531,\n", "     203.9314422607422],\n", "    [81.14381408691406,\n", "     304.8990783691406,\n", "     94.82292175292969,\n", "     317.7912292480469],\n", "    [133.2362518310547,\n", "     135.33883666992188,\n", "     143.44114685058594,\n", "     144.49520874023438],\n", "    [67.99366760253906,\n", "     30.636964797973633,\n", "     78.67538452148438,\n", "     40.703800201416016],\n", "    [0.3443622589111328,\n", "     139.1008758544922,\n", "     14.191765785217285,\n", "     152.3331756591797],\n", "    [-0.20622968673706055,\n", "     48.37458038330078,\n", "     13.53518295288086,\n", "     62.136558532714844],\n", "    [305.9346618652344,\n", "     128.34298706054688,\n", "     316.2181701660156,\n", "     138.22409057617188],\n", "    [58.35306167602539,\n", "     38.78727722167969,\n", "     69.56433868408203,\n", "     49.535377502441406],\n", "    [210.24717712402344, 336.751953125, 223.5063018798828, 349.56317138671875],\n", "    [144.9632568359375, 272.5302734375, 157.6673583984375, 284.6192626953125],\n", "    [294.6093444824219,\n", "     134.7933807373047,\n", "     304.9302673339844,\n", "     144.6428985595703],\n", "    [344.5365905761719,\n", "     11.122383117675781,\n", "     356.9715270996094,\n", "     23.389556884765625],\n", "    [178.39663696289062,\n", "     194.87962341308594,\n", "     190.1767578125,\n", "     206.1378936767578],\n", "    [228.38314819335938,\n", "     304.9832763671875,\n", "     238.31625366210938,\n", "     314.1861572265625],\n", "    [242.5550079345703,\n", "     18.030433654785156,\n", "     254.33213806152344,\n", "     29.277481079101562],\n", "    [97.4253158569336,\n", "     349.4041748046875,\n", "     109.0808334350586,\n", "     361.1558837890625],\n", "    [245.96827697753906,\n", "     335.96771240234375,\n", "     256.8175354003906,\n", "     346.39361572265625],\n", "    [208.92855834960938,\n", "     241.03013610839844,\n", "     222.17999267578125,\n", "     253.68946838378906],\n", "    [321.119140625, 31.767108917236328, 334.3251953125, 44.4337043762207],\n", "    [49.48479080200195,\n", "     144.8072052001953,\n", "     61.69839859008789,\n", "     156.5552520751953],\n", "    [67.07374572753906, 22.018714904785156, 78.23603820800781, 32.70751953125],\n", "    [69.73357391357422,\n", "     67.74176025390625,\n", "     81.41260528564453,\n", "     79.11349487304688],\n", "    [343.0590515136719, 80.415771484375, 354.0903625488281, 91.22297668457031],\n", "    ...]}],\n", " 'visualization': []}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "from mmdet.apis import DetInferencer\n", "\n", "# Choose to use a config\n", "config_path = r\"E:\\tobacco\\tobacco_det\\work_dirs\\config\\config.py\"\n", "\n", "# Setup a checkpoint file to load\n", "checkpoint = r\"E:\\tobacco\\tobacco_det\\work_dirs\\config\\epoch_160.pth\"\n", "\n", "# Initialize the DetInferencer\n", "inferencer = DetInferencer(model=config_path, weights=checkpoint, device=\"cuda:0\")\n", "\n", "inferencer(r\"E:\\tobacco\\tobacco_det_data\\batch2_384\\images\\tod_P061301_1.tif\")"]}, {"cell_type": "markdown", "metadata": {"id": "b-AlYOw4T3AO"}, "source": ["- Passing config file to `model` without specifying `weight` will result in a randomly initialized model."]}, {"cell_type": "markdown", "metadata": {"id": "0a4Zw5plUisX"}, "source": ["### Inference\n", "\n", "Once the Inferencer is initialized, you can directly pass in the raw data to be inferred and get the inference results from return values.\n", "\n", "#### Input\n", "\n", "Input can be either of these types:\n", "\n", "- str: Path/URL to the image."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["2abd7eef6f1f4b9c865a466b3dd5ef24", "8951ec1ee7164f7ca7239a37e80e98ea"]}, "id": "C4McAmYdUnCL", "outputId": "50bea3e2-a912-497e-cee9-26109dccdc12"}, "outputs": [], "source": ["inferencer = DetInferencer(model=\"rtmdet_tiny_8xb32-300e_coco\", device=\"cuda:0\")\n", "inferencer(\"demo/demo.jpg\")"]}, {"cell_type": "markdown", "metadata": {"id": "3G_TPKrMUp2T"}, "source": ["- array: Image in numpy array. It should be in BGR order."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["59bfd22c751f4ed4baefa466e7653315", "0164804ae2f842fe8d2a4c5414c4a0c2"]}, "id": "-M1qGlfaUpha", "outputId": "5a06cfe8-e056-4d56-c8e9-489e8f6633a0"}, "outputs": [], "source": ["import mmcv\n", "\n", "array = mmcv.imread(\"demo/demo.jpg\")\n", "inferencer(array)"]}, {"cell_type": "markdown", "metadata": {"id": "I45B_CtzUuh2"}, "source": ["- list: A list of basic types above. Each element in the list will be processed separately."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["a64a6eb038c44236b80579b2bfc4b8e3", "eef25a0509854f98883395a2c0fc2134", "f87f0b153b0342ad99dcd320a1302c92", "f6634888109048069b6844e9f9b4ec13"]}, "id": "k1IXIWXHUwKP", "outputId": "0af73b0b-d703-4cbc-91ad-052f0b521d50"}, "outputs": [], "source": ["inferencer([\"tests/data/color.jpg\", \"tests/data/gray.jpg\"])\n", "# You can even mix the types\n", "inferencer([\"tests/data/color.jpg\", array])"]}, {"cell_type": "markdown", "metadata": {"id": "hUGrTtxrVBAS"}, "source": ["- str: Path to the directory. All images in the directory will be processed."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["07ed8efcd87a40059af36f0c43ef5147", "69ce7e58e27f4e1186ab0afcb99d37c3"]}, "id": "JWK10ZD6VDDE", "outputId": "91418597-d9ea-4613-b141-16bc8bcc8caf"}, "outputs": [], "source": ["inferencer(\"tests/data/\")"]}, {"cell_type": "markdown", "metadata": {"id": "BQxEVr2pVGen"}, "source": ["### Output\n", "\n", "By default, each `Inferencer` returns the prediction results in a dictionary format.\n", "\n", "- `visualization` contains the visualized predictions.\n", "\n", "- `predictions` contains the predictions results in a json-serializable format. But it's an empty list by default unless `return_vis=True`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 306, "referenced_widgets": ["95674a6baa1842d2981fe60b31ab6cad", "7e62816d1f6c441fb98c1f8e942fff1d"]}, "id": "m6a8T4goU8Sq", "outputId": "6f74098f-a3d3-4897-c58f-68fae88889af"}, "outputs": [], "source": ["# Show the structure of result dict\n", "from rich.pretty import pprint\n", "\n", "result = inferencer(\"demo/demo.jpg\")\n", "pprint(result, max_length=4)"]}, {"cell_type": "markdown", "metadata": {"id": "a93hFT0jVkrR"}, "source": ["If you wish to get the raw outputs from the model, you can set `return_datasamples` to `True` to get the original `DataSample`, which will be stored in `predictions`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["060f510b5bda498583d7212060bb528c", "1bb724cb12c240a18f651dd99842e5b0"]}, "id": "U5DFI7QAVbnP", "outputId": "effaf3ec-2476-4b64-dcbd-802a18a26479"}, "outputs": [], "source": ["result = inferencer(\"demo/demo.jpg\", return_datasamples=True)\n", "pprint(result, max_length=4)"]}, {"cell_type": "markdown", "metadata": {"id": "JHdcUnGzVsk1"}, "source": ["#### Dumping Results\n", "\n", "Apart from obtaining predictions from the return value, you can also export the predictions/visualizations to files by setting `out_dir` and `no_save_pred`/`no_save_vis` arguments."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["38083c2f29604d1d9a7dcf9845dfbf33", "54cdfe55e0f04df9ab844961a089fe2f"]}, "id": "0dr-ixmfVtng", "outputId": "af22d458-9aed-41e2-f675-e017a0cb588b"}, "outputs": [], "source": ["inferencer(\"demo/demo.jpg\", out_dir=\"outputs/\", no_save_pred=False)"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "kernelspec": {"display_name": "openmmlab", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0164804ae2f842fe8d2a4c5414c4a0c2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0226fedc26044ab2abdccc4fcbe226f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "060f510b5bda498583d7212060bb528c": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_1bb724cb12c240a18f651dd99842e5b0", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span>  <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m  \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "07ed8efcd87a40059af36f0c43ef5147": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_69ce7e58e27f4e1186ab0afcb99d37c3", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span> <span style=\"color: #800080; text-decoration-color: #800080\">13.5 it/s</span> <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m \u001b[35m13.5 it/s\u001b[0m \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "1bb724cb12c240a18f651dd99842e5b0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2abd7eef6f1f4b9c865a466b3dd5ef24": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_8951ec1ee7164f7ca7239a37e80e98ea", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span>  <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m  \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "38083c2f29604d1d9a7dcf9845dfbf33": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_54cdfe55e0f04df9ab844961a089fe2f", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span>  <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m  \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "54cdfe55e0f04df9ab844961a089fe2f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59bfd22c751f4ed4baefa466e7653315": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_0164804ae2f842fe8d2a4c5414c4a0c2", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span>  <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m  \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "69ce7e58e27f4e1186ab0afcb99d37c3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6fa2cda48fda43f9bf53a0f533392eba": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_0226fedc26044ab2abdccc4fcbe226f8", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span>  <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m  \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "7e62816d1f6c441fb98c1f8e942fff1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8951ec1ee7164f7ca7239a37e80e98ea": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95674a6baa1842d2981fe60b31ab6cad": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_7e62816d1f6c441fb98c1f8e942fff1d", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span>  <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m  \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "a64a6eb038c44236b80579b2bfc4b8e3": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_eef25a0509854f98883395a2c0fc2134", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span> <span style=\"color: #800080; text-decoration-color: #800080\">9.7 it/s</span> <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m \u001b[35m9.7 it/s\u001b[0m \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}, "eef25a0509854f98883395a2c0fc2134": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f6634888109048069b6844e9f9b4ec13": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f87f0b153b0342ad99dcd320a1302c92": {"model_module": "@jupyter-widgets/output", "model_module_version": "1.0.0", "model_name": "OutputModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_f6634888109048069b6844e9f9b4ec13", "msg_id": "", "outputs": [{"data": {"text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">Inference <span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #f92672; text-decoration-color: #f92672\">━</span><span style=\"color: #f42670; text-decoration-color: #f42670\">━</span><span style=\"color: #e6276c; text-decoration-color: #e6276c\">━</span><span style=\"color: #d12a66; text-decoration-color: #d12a66\">━</span><span style=\"color: #b72c5e; text-decoration-color: #b72c5e\">━</span><span style=\"color: #993056; text-decoration-color: #993056\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #3a3a3a; text-decoration-color: #3a3a3a\">━</span><span style=\"color: #3e393b; text-decoration-color: #3e393b\">━</span><span style=\"color: #4c383f; text-decoration-color: #4c383f\">━</span><span style=\"color: #613545; text-decoration-color: #613545\">━</span><span style=\"color: #7b334d; text-decoration-color: #7b334d\">━</span> <span style=\"color: #800080; text-decoration-color: #800080\">9.0 it/s</span> <span style=\"color: #008080; text-decoration-color: #008080\"> </span>\n</pre>\n", "text/plain": "Inference \u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;249;38;114m━\u001b[0m\u001b[38;2;244;38;112m━\u001b[0m\u001b[38;2;230;39;108m━\u001b[0m\u001b[38;2;209;42;102m━\u001b[0m\u001b[38;2;183;44;94m━\u001b[0m\u001b[38;2;153;48;86m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;58;58;58m━\u001b[0m\u001b[38;2;62;57;59m━\u001b[0m\u001b[38;2;76;56;63m━\u001b[0m\u001b[38;2;97;53;69m━\u001b[0m\u001b[38;2;123;51;77m━\u001b[0m \u001b[35m9.0 it/s\u001b[0m \u001b[36m \u001b[0m\n"}, "metadata": {}, "output_type": "display_data"}]}}}}}, "nbformat": 4, "nbformat_minor": 0}