from ultralytics import YOLO
import rasterio
import numpy as np

model = YOLO(
    r"D:\ZhangChi\tobacco_new\runs\detect\train3\weights\best.pt"
)  # load a pretrained model (recommended for training)

with rasterio.open(
    r"D:\ZhangChi\tobacco_new\yolo_dataset\images\val\tobacco_0713_1.tif"
) as src:
    img = src.read([3, 2, 1]).transpose(1, 2, 0)
    img = np.ascontiguousarray(img)

results = model.predict(
    [img],
    conf=0.4,
    iou=0.4,
    show=True,
    line_width=1,
    imgsz=1024,
    max_det=4096,
    show_labels=False,
    show_conf=True,
)
input()
# Process results list
# for result in results:
#     boxes = result.boxes  # Boxes object for bounding box outputs
#     masks = result.masks  # Masks object for segmentation masks outputs
#     keypoints = result.keypoints  # Keypoints object for pose outputs
#     probs = result.probs  # Probs object for classification outputs
#     obb = result.obb  # Oriented boxes object for OBB outputs
#     result.show()  # display to screen
#     # result.save(filename="result.jpg")  # save to disk
