#!/usr/bin/env python3
"""
Enhanced YOLO inference script with image saving and visualization options.
Supports various output formats and customization options.
"""

import argparse
import sys
from pathlib import Path
from typing import Optional, Tuple, List
import json

import cv2
import numpy as np
from ultralytics import <PERSON><PERSON><PERSON>


def save_detection_image(
    model_path: str,
    image_path: str,
    output_path: Optional[str] = None,
    conf: float = 0.3,
    iou: float = 0.3,
    imgsz: int = 384,
    max_det: int = 4096,
    line_width: int = 2,
    show_labels: bool = True,
    show_conf: bool = True,
    show: bool = False,
    save_txt: bool = False,
    save_json: bool = False,
    box_color: Tuple[int, int, int] = (0, 255, 0),
    text_color: Tuple[int, int, int] = (255, 255, 255),
    font_scale: float = 0.5,
    verbose: bool = True
) -> dict:
    """
    Run YOLO inference and save the image with detection visualizations.
    
    Args:
        model_path: Path to YOLO model weights
        image_path: Path to input image
        output_path: Path to save output image (if None, auto-generate)
        conf: Confidence threshold
        iou: IoU threshold
        imgsz: Input image size
        max_det: Maximum detections
        line_width: Bounding box line width
        show_labels: Show class labels
        show_conf: Show confidence scores
        show: Display image window
        save_txt: Save detection results as text file
        save_json: Save detection results as JSON file
        box_color: RGB color for bounding boxes
        text_color: RGB color for text
        font_scale: Font scale for text
        verbose: Print detailed information
    
    Returns:
        Dictionary with results information
    """
    if verbose:
        print(f"Loading model: {model_path}")
    
    # Load model
    model = YOLO(model_path)
    
    # Generate output path if not provided
    if output_path is None:
        input_path = Path(image_path)
        output_dir = input_path.parent / "detections"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / f"{input_path.stem}_detections.jpg"
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    if verbose:
        print(f"Processing image: {image_path}")
    
    # Run inference
    results = model.predict(
        image_path,
        conf=conf,
        iou=iou,
        show=show,
        save=False,  # We'll handle saving manually
        line_width=line_width,
        imgsz=imgsz,
        max_det=max_det,
        verbose=False
    )
    
    result_info = {
        "image_path": str(image_path),
        "output_path": str(output_path),
        "num_detections": 0,
        "detections": []
    }
    
    if results and len(results) > 0:
        result = results[0]
        
        # Get the annotated image with custom styling
        annotated_img = result.plot(
            line_width=line_width,
            labels=show_labels,
            conf=show_conf,
            pil=False  # Return as numpy array
        )
        
        # Save the annotated image
        cv2.imwrite(str(output_path), annotated_img)
        
        if verbose:
            print(f"Detection image saved to: {output_path}")
        
        # Extract detection information
        if result.boxes is not None:
            boxes = result.boxes
            num_detections = len(boxes)
            result_info["num_detections"] = num_detections
            
            if verbose:
                print(f"Found {num_detections} detections")
            
            # Process each detection
            for i, (box, conf_score, cls) in enumerate(zip(
                boxes.xyxy.cpu().numpy(),
                boxes.conf.cpu().numpy(),
                boxes.cls.cpu().numpy()
            )):
                x1, y1, x2, y2 = box
                detection_info = {
                    "id": i,
                    "class": int(cls),
                    "confidence": float(conf_score),
                    "bbox": {
                        "x1": float(x1),
                        "y1": float(y1),
                        "x2": float(x2),
                        "y2": float(y2),
                        "width": float(x2 - x1),
                        "height": float(y2 - y1),
                        "center_x": float((x1 + x2) / 2),
                        "center_y": float((y1 + y2) / 2)
                    }
                }
                result_info["detections"].append(detection_info)
            
            if verbose and num_detections > 0:
                confidences = boxes.conf.cpu().numpy()
                print(f"Confidence range: {confidences.min():.3f} - {confidences.max():.3f}")
        else:
            if verbose:
                print("No detections found")
        
        # Save text file if requested
        if save_txt:
            txt_path = output_path.with_suffix('.txt')
            with open(txt_path, 'w') as f:
                for det in result_info["detections"]:
                    bbox = det["bbox"]
                    # YOLO format: class center_x center_y width height (normalized)
                    img_h, img_w = annotated_img.shape[:2]
                    norm_cx = bbox["center_x"] / img_w
                    norm_cy = bbox["center_y"] / img_h
                    norm_w = bbox["width"] / img_w
                    norm_h = bbox["height"] / img_h
                    f.write(f"{det['class']} {norm_cx:.6f} {norm_cy:.6f} {norm_w:.6f} {norm_h:.6f}\n")
            
            if verbose:
                print(f"Detection coordinates saved to: {txt_path}")
        
        # Save JSON file if requested
        if save_json:
            json_path = output_path.with_suffix('.json')
            with open(json_path, 'w') as f:
                json.dump(result_info, f, indent=2)
            
            if verbose:
                print(f"Detection results saved to: {json_path}")
    
    return result_info


def batch_save_images(
    model_path: str,
    input_dir: str,
    output_dir: str,
    pattern: str = "*.tif",
    **kwargs
) -> List[dict]:
    """
    Process multiple images in a directory.
    
    Args:
        model_path: Path to YOLO model
        input_dir: Input directory containing images
        output_dir: Output directory for results
        pattern: File pattern to match (e.g., "*.tif", "*.jpg")
        **kwargs: Additional arguments for save_detection_image
    
    Returns:
        List of result dictionaries
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Find matching files
    image_files = list(input_path.glob(pattern))
    
    if not image_files:
        print(f"No files found matching pattern '{pattern}' in {input_dir}")
        return []
    
    print(f"Processing {len(image_files)} images...")
    
    results = []
    for i, image_file in enumerate(image_files, 1):
        print(f"\nProcessing {i}/{len(image_files)}: {image_file.name}")
        
        output_file = output_path / f"{image_file.stem}_detections.jpg"
        
        try:
            result = save_detection_image(
                model_path=model_path,
                image_path=str(image_file),
                output_path=str(output_file),
                **kwargs
            )
            results.append(result)
        except Exception as e:
            print(f"Error processing {image_file.name}: {e}")
            continue
    
    print(f"\nCompleted processing {len(results)} images")
    return results


def main():
    """Command-line interface."""
    parser = argparse.ArgumentParser(
        description="YOLO inference with image saving",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument("--model", "-m", required=True, help="Path to YOLO model")
    parser.add_argument("--input", "-i", required=True, help="Input image or directory")
    parser.add_argument("--output", "-o", help="Output path")
    parser.add_argument("--conf", type=float, default=0.3, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.3, help="IoU threshold")
    parser.add_argument("--imgsz", type=int, default=384, help="Image size")
    parser.add_argument("--max-det", type=int, default=4096, help="Maximum detections")
    parser.add_argument("--line-width", type=int, default=2, help="Line width")
    parser.add_argument("--no-labels", action="store_true", help="Hide labels")
    parser.add_argument("--no-conf", action="store_true", help="Hide confidence")
    parser.add_argument("--show", action="store_true", help="Show image window")
    parser.add_argument("--save-txt", action="store_true", help="Save as text file")
    parser.add_argument("--save-json", action="store_true", help="Save as JSON file")
    parser.add_argument("--pattern", default="*.tif", help="File pattern for batch processing")
    parser.add_argument("--quiet", "-q", action="store_true", help="Suppress output")
    
    args = parser.parse_args()
    
    # Check if input is file or directory
    input_path = Path(args.input)
    
    if input_path.is_file():
        # Single file processing
        result = save_detection_image(
            model_path=args.model,
            image_path=args.input,
            output_path=args.output,
            conf=args.conf,
            iou=args.iou,
            imgsz=args.imgsz,
            max_det=args.max_det,
            line_width=args.line_width,
            show_labels=not args.no_labels,
            show_conf=not args.no_conf,
            show=args.show,
            save_txt=args.save_txt,
            save_json=args.save_json,
            verbose=not args.quiet
        )
        
        if not args.quiet:
            print(f"Processed 1 image with {result['num_detections']} detections")
    
    elif input_path.is_dir():
        # Batch processing
        output_dir = args.output or str(input_path / "detections")
        
        results = batch_save_images(
            model_path=args.model,
            input_dir=args.input,
            output_dir=output_dir,
            pattern=args.pattern,
            conf=args.conf,
            iou=args.iou,
            imgsz=args.imgsz,
            max_det=args.max_det,
            line_width=args.line_width,
            show_labels=not args.no_labels,
            show_conf=not args.no_conf,
            show=args.show,
            save_txt=args.save_txt,
            save_json=args.save_json,
            verbose=not args.quiet
        )
        
        if not args.quiet:
            total_detections = sum(r['num_detections'] for r in results)
            print(f"Processed {len(results)} images with {total_detections} total detections")
    
    else:
        print(f"Error: {args.input} is not a valid file or directory")
        sys.exit(1)


if __name__ == "__main__":
    main()
