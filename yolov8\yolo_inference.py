import argparse
import warnings
from pathlib import Path
from typing import List, Tuple

import geopandas as gpd
import numpy as np
import rasterio
from rasterio.windows import Window
from shapely.geometry import Point, Polygon
from ultralytics import YOLO

warnings.filterwarnings("ignore", category=UserWarning)


def create_sliding_windows(
    height: int, width: int, block_size: int, overlap: int
) -> List[Tuple[int, int, int, int]]:
    """
    Create sliding windows for processing large images in blocks.

    Args:
        height: Image height in pixels
        width: Image width in pixels
        block_size: Size of each block in pixels
        overlap: Overlap between blocks in pixels

    Returns:
        List of tuples (row_start, col_start, row_end, col_end)
    """
    blocks = []
    step = block_size - overlap

    for i in range(0, height, step):
        for j in range(0, width, step):
            # Adjust boundaries to ensure we don't exceed image dimensions
            row_start = i
            col_start = j
            row_end = min(i + block_size, height)
            col_end = min(j + block_size, width)

            # Ensure minimum block size
            if row_end - row_start >= block_size // 2 and col_end - col_start >= block_size // 2:
                blocks.append((row_start, col_start, row_end, col_end))

    # Remove duplicates while preserving order
    seen = set()
    unique_blocks = []
    for block in blocks:
        if block not in seen:
            seen.add(block)
            unique_blocks.append(block)

    return unique_blocks


def convert_to_geographic_coordinates(
    bboxes: np.ndarray,
    transform: rasterio.Affine,
    output_format: str = "center"
) -> List:
    """
    Convert pixel coordinates to geographic coordinates.

    Args:
        bboxes: Array of bounding boxes in pixel coordinates [x1, y1, x2, y2]
        transform: Rasterio affine transform
        output_format: "center" for point geometries, "bbox" for polygon geometries

    Returns:
        List of shapely geometries (Points or Polygons)
    """
    geometries = []

    for bbox in bboxes:
        x1, y1, x2, y2 = bbox

        if output_format == "center":
            # Calculate center point
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            # Transform to geographic coordinates
            geo_coords = transform * (center_x, center_y)
            geometries.append(Point(geo_coords[0], geo_coords[1]))

        elif output_format == "bbox":
            # Transform all four corners
            corner_coords = [
                transform * (x1, y1),  # top-left
                transform * (x2, y1),  # top-right
                transform * (x2, y2),  # bottom-right
                transform * (x1, y2),  # bottom-left
            ]
            corners = [(coord[0], coord[1]) for coord in corner_coords]
            geometries.append(Polygon(corners))

    return geometries


def non_max_suppression(boxes: np.ndarray, scores: np.ndarray, iou_threshold: float = 0.5) -> np.ndarray:
    """
    Apply Non-Maximum Suppression to remove overlapping detections.

    Args:
        boxes: Array of bounding boxes [x1, y1, x2, y2]
        scores: Array of confidence scores
        iou_threshold: IoU threshold for suppression

    Returns:
        Array of indices to keep
    """
    if len(boxes) == 0:
        return np.array([])

    # Calculate areas
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

    # Sort by scores in descending order
    order = scores.argsort()[::-1]

    keep = []
    while len(order) > 0:
        # Keep the box with highest score
        i = order[0]
        keep.append(i)

        if len(order) == 1:
            break

        # Calculate IoU with remaining boxes
        xx1 = np.maximum(boxes[i, 0], boxes[order[1:], 0])
        yy1 = np.maximum(boxes[i, 1], boxes[order[1:], 1])
        xx2 = np.minimum(boxes[i, 2], boxes[order[1:], 2])
        yy2 = np.minimum(boxes[i, 3], boxes[order[1:], 3])

        w = np.maximum(0, xx2 - xx1)
        h = np.maximum(0, yy2 - yy1)
        inter = w * h

        ovr = inter / (areas[i] + areas[order[1:]] - inter)

        # Keep boxes with IoU less than threshold
        inds = np.where(ovr <= iou_threshold)[0]
        order = order[inds + 1]

    return np.array(keep)


def yolo_inference_to_shp(
    model_path: str,
    img_path: str,
    output_shp_path: str,
    block_size: int = 1024,
    overlap_ratio: float = 0.1,
    conf_threshold: float = 0.3,
    iou_threshold: float = 0.5,
    nms_iou_threshold: float = 0.5,
    output_format: str = "center",
    verbose: bool = True
) -> None:
    """
    Run YOLO inference on a TIFF image and save results as shapefile.

    Args:
        model_path: Path to YOLO model weights
        img_path: Path to input TIFF image
        output_shp_path: Path to output shapefile
        block_size: Size of processing blocks in pixels
        overlap_ratio: Overlap ratio between blocks (0.0 to 1.0)
        conf_threshold: Confidence threshold for detections
        iou_threshold: IoU threshold for YOLO inference
        nms_iou_threshold: IoU threshold for Non-Maximum Suppression
        output_format: "center" for points, "bbox" for polygons
        verbose: Print progress information
    """
    if verbose:
        print(f"Loading YOLO model from: {model_path}")

    # Load YOLO model
    model = YOLO(model_path)

    # Open TIFF image with rasterio to get spatial reference
    if verbose:
        print(f"Opening TIFF image: {img_path}")

    with rasterio.open(img_path) as src:
        # Get spatial reference information
        transform = src.transform
        crs = src.crs
        height, width = src.shape

        if verbose:
            print(f"Image dimensions: {width} x {height}")
            print(f"Spatial reference: {crs}")
            print(f"Transform: {transform}")

        # Calculate overlap in pixels
        overlap = int(overlap_ratio * block_size)

        # Create sliding windows
        blocks = create_sliding_windows(height, width, block_size, overlap)

        if verbose:
            print(f"Processing {len(blocks)} blocks with size {block_size}x{block_size} and overlap {overlap}")

        # Store all detections
        all_bboxes = []
        all_scores = []
        all_classes = []

        # Process each block
        for i, (row_start, col_start, row_end, col_end) in enumerate(blocks):
            if verbose and i % 10 == 0:
                print(f"Processing block {i+1}/{len(blocks)}")

            # Read image block
            window = Window.from_slices(
                (row_start, row_end),
                (col_start, col_end)
            )

            # Read RGB bands (assuming bands 1,2,3 are RGB)
            try:
                block_data = src.read([1, 2, 3], window=window)
                block_img = np.transpose(block_data, (1, 2, 0))
                block_img = np.ascontiguousarray(block_img)
            except Exception as e:
                if verbose:
                    print(f"Error reading block {i}: {e}")
                continue

            # Run YOLO inference
            try:
                results = model.predict(
                    block_img,
                    conf=conf_threshold,
                    iou=iou_threshold,
                    verbose=False,
                    save=False
                )

                # Extract detections
                if results and len(results) > 0 and results[0].boxes is not None:
                    # Get boxes, scores, and classes as numpy arrays
                    # Handle both CPU and CUDA tensors using try-except
                    try:
                        # Try to get as numpy directly (for CPU tensors/arrays)
                        boxes = np.array(results[0].boxes.xyxy)
                        scores = np.array(results[0].boxes.conf)
                        classes = np.array(results[0].boxes.cls)
                    except (RuntimeError, TypeError):
                        # If that fails, move to CPU first (for CUDA tensors)
                        boxes = results[0].boxes.xyxy.cpu().numpy()  # type: ignore
                        scores = results[0].boxes.conf.cpu().numpy()  # type: ignore
                        classes = results[0].boxes.cls.cpu().numpy()  # type: ignore

                    # Convert block coordinates to full image coordinates
                    boxes[:, [0, 2]] += col_start  # x coordinates
                    boxes[:, [1, 3]] += row_start  # y coordinates

                    all_bboxes.append(boxes)
                    all_scores.append(scores)
                    all_classes.append(classes)

            except Exception as e:
                if verbose:
                    print(f"Error in inference for block {i}: {e}")
                continue

    # Combine all detections
    if len(all_bboxes) == 0:
        if verbose:
            print("No detections found!")
        # Create empty GeoDataFrame
        gdf = gpd.GeoDataFrame({
            'geometry': [],
            'confidence': [],
            'class': []
        }, crs=crs)
    else:
        all_bboxes = np.vstack(all_bboxes)
        all_scores = np.concatenate(all_scores)
        all_classes = np.concatenate(all_classes)

        if verbose:
            print(f"Found {len(all_bboxes)} total detections before NMS")

        # Apply Non-Maximum Suppression
        keep_indices = non_max_suppression(all_bboxes, all_scores, nms_iou_threshold)

        all_bboxes = all_bboxes[keep_indices]
        all_scores = all_scores[keep_indices]
        all_classes = all_classes[keep_indices]

        if verbose:
            print(f"Kept {len(all_bboxes)} detections after NMS")

        # Convert to geographic coordinates
        geometries = convert_to_geographic_coordinates(all_bboxes, transform, output_format)

        # Create GeoDataFrame
        gdf = gpd.GeoDataFrame({
            'geometry': geometries,
            'confidence': all_scores,
            'class': all_classes.astype(int),
            'bbox_x1': all_bboxes[:, 0],
            'bbox_y1': all_bboxes[:, 1],
            'bbox_x2': all_bboxes[:, 2],
            'bbox_y2': all_bboxes[:, 3]
        }, crs=crs)

    # Save to shapefile
    if verbose:
        print(f"Saving {len(gdf)} detections to: {output_shp_path}")

    # Ensure output directory exists
    output_path = Path(output_shp_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Save shapefile
    gdf.to_file(output_shp_path, encoding="utf-8")

    if verbose:
        print("Inference completed successfully!")
        if len(gdf) > 0:
            print(f"Confidence range: {gdf['confidence'].min():.3f} - {gdf['confidence'].max():.3f}")


def main():
    parser = argparse.ArgumentParser(description="YOLO inference with geographic coordinate conversion")
    parser.add_argument("--model", required=True, help="Path to YOLO model weights")
    parser.add_argument("--image", required=True, help="Path to input TIFF image")
    parser.add_argument("--output", required=True, help="Path to output shapefile")
    parser.add_argument("--block-size", type=int, default=384, help="Processing block size")
    parser.add_argument("--overlap", type=float, default=0, help="Overlap ratio between blocks")
    parser.add_argument("--conf", type=float, default=0.3, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.8, help="IoU threshold for YOLO")
    parser.add_argument("--nms-iou", type=float, default=0.8, help="IoU threshold for NMS")
    parser.add_argument("--format", choices=["center", "bbox"], default="center",
                       help="Output format: center points or bounding box polygons")
    parser.add_argument("--quiet", action="store_true", help="Suppress verbose output")

    args = parser.parse_args()

    yolo_inference_to_shp(
        model_path=args.model,
        img_path=args.image,
        output_shp_path=args.output,
        block_size=args.block_size,
        overlap_ratio=args.overlap,
        conf_threshold=args.conf,
        iou_threshold=args.iou,
        nms_iou_threshold=args.nms_iou,
        output_format=args.format,
        verbose=not args.quiet
    )


if __name__ == "__main__":
    main()