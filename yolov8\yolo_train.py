from ultralytics import YOLO

if __name__ == "__main__":
    # Load a model
    # model = YOLO("yolov8m.yaml")  # build a new model from scratch
    model = YOLO("yolov8s.pt")  # load a pretrained model (recommended for training)

    # Use the model
    model.train(
        data=r"E:\tobacco\tobacco_det_data\batch2_384\train_data\yolo\data.yaml",
        epochs=300,
        imgsz=384,
        batch=20,
        cache="memory",
        device=[0],
        amp=True,
        cos_lr=True,
        dropout=0.1,
        flipud=0.5,
        fliplr=0.5,
        optimizer="AdamW",
        lr0=0.0001,
        # iou=0.8,
        multi_scale=True,
    )  # train the model
    # metrics = model.val()  # evaluate model performance on the validation set
    # results = model("https://ultralytics.com/images/bus.jpg")  # predict on an image
    # path = model.export(format="torchscript")  # export the model to ONNX format
    # print(path)
