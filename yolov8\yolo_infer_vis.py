from pathlib import Path

import geopandas as gpd
import rasterio
from shapely.geometry import Point
from ultralytics import YOL<PERSON>


def save_detection_shp(
    model_path,
    image_path,
    output_path,
    conf: float = 0.3,
    iou: float = 0.3,
    imgsz: int = 384,
    max_det: int = 4096,
    line_width: int = 2,
    show_labels: bool = True,
    show_conf: bool = True,
    show: bool = False,
):
    """
    Run YOLO inference and save the image with detection visualizations.

    Args:
        model_path: Path to YOLO model weights
        image_path: Path to input image
        output_path: Path to save output image (if None, auto-generate)
        conf: Confidence threshold
        iou: IoU threshold
        imgsz: Input image size
        max_det: Maximum detections
        line_width: Bounding box line width
        show_labels: Show class labels
        show_conf: Show confidence scores
        show: Display image window

    Returns:
        Path to saved image
    """
    # Load model
    model = YOLO(model_path)
    # Run inference with save option
    result = model.predict(
        image_path,
        conf=conf,
        iou=iou,
        show=False,
        save=True,  # This saves to runs/detect/predict
        line_width=line_width,
        imgsz=imgsz,
        max_det=max_det,
        show_labels=show_labels,
        show_conf=show_conf,
        project="output",  # Custom project directory
        name="detections",  # Custom run name
        exist_ok=True,  # Overwrite existing results
    )[0]

    # 1. 读取TIFF文件获取空间参考
    with rasterio.open(image_path) as src:
        transform = src.transform  # 仿射变换矩阵
        crs = src.crs  # 坐标系（如EPSG:32650）

    # 2. 定义bbox列表（示例，格式：[[xmin, ymin, xmax, ymax], ...]）
    centers_pixel = result.boxes.xywh[:, :2].cpu().numpy()
    centers_geo = transform * (centers_pixel[:, 0], centers_pixel[:, 1])
    points_geo = [Point(x, y) for x, y in zip(centers_geo[0], centers_geo[1])]

    # 4. 创建GeoDataFrame
    gdf = gpd.GeoDataFrame(geometry=points_geo, crs=crs)
    print("数据量:", len(gdf))

    # 5. 保存为Shapefile
    gdf.to_file(output_path, driver="Arrow")

    print(f"已经保存到: {output_path}")


if __name__ == "__main__":
    # Configuration
    model_path = r"E:\tobacco\tobacco_det\runs\detect\train3(yolov8_real)\weights\best.pt"
    image_dir = r"E:\tobacco\tobacco_det_data\batch2_384\images"
    output_dir = Path(r"E:\tobacco\tobacco_det_data\batch2_384\approach\yolov8s\inference")

    for tiff in Path(image_dir).glob("*.tif"):
        output_path = output_dir / f"{tiff.stem}_yolov8s.arrow"
        # Save detection image
        output = save_detection_shp(
            model_path,
            tiff,
            output_path,
            conf=0.3,
            iou=0.3,
            imgsz=384,
            max_det=4096,
            line_width=1,
            show_labels=False,
            show_conf=True,
            show=True,  # Set to False if you don't want to display the window
        )

    # print(f"Detection visualization saved to: {output_path}")
