import argparse
import sys
from pathlib import Path

import geopandas as gpd
import numpy as np
import rasterio
import torch
import tqdm
from mmdet.apis import inference_detector, init_detector
from mmdet.utils import register_all_modules

# from numba import njit
from rasterio.windows import Window
from shapely.geometry import Point


def main(
    config_file,
    checkpoint_file,
    img_path,
    output_shp_path,
    device,
    score_threshold,
):
    src = rasterio.open(img_path)
    transform = src.transform

    # Register all modules in mmdet into the registries
    register_all_modules()
    # build the model from a config file and a checkpoint file
    model = init_detector(config_file, checkpoint_file, device=device)

    probability_list = []
    bbox_list = []

    img = src.read([1, 2, 3]).transpose(1, 2, 0)

    result = inference_detector(model, img)

    scores = result.pred_instances.scores.cpu().numpy()
    bboxes = result.pred_instances.bboxes.cpu().numpy()
    good_results = scores > score_threshold
    scores = scores[good_results]
    bboxes = bboxes[good_results]

    for bbox, score in zip(bboxes, scores):
        probability_list.append(score.item())
        bbox_list.append(bbox)

    src.close()

    if len(bbox_list) == 0 or len(probability_list) == 0:
        return

    # 非极大值抑制NMS减少多余边界框
    bbox_list = np.array(bbox_list)
    probability_list = np.array(probability_list).reshape(-1, 1)

    points = []
    x_percent = 0.5
    y_percent = 0.5
    for bbox, _ in zip(bbox_list, probability_list):
        xmin, ymin, xmax, ymax = bbox
        x_coord = xmin + (xmax - xmin) * x_percent
        y_coord = ymin + (ymax - ymin) * y_percent
        transformed_point = transform * (x_coord, y_coord)
        points.append(Point(transformed_point))
    
    output_file = gpd.GeoDataFrame(geometry=points, crs=src.crs)
    output_file["score"] = probability_list

    if len(output_file) > 0:
        output_file.to_file(output_shp_path, encoding="utf-8")


def run(img_path_, output_shp_path_):
    model_config = Path(r"E:\tobacco\tobacco_det\work_dirs\config\config.py").resolve().as_posix()
    model_path = Path(r"E:\tobacco\tobacco_det\work_dirs\config\epoch_160.pth").resolve().as_posix()
    device = "cuda" if torch.cuda.is_available() else "cpu"
    score_threshold = 0.3

    main(
        model_config,
        model_path,
        img_path_,
        output_shp_path_,
        device,
        score_threshold,
    )

if __name__ == "__main__":
    img_path = Path(r"E:\tobacco\tobacco_det_data\batch2_384\images\tod_P061301_1.tif").resolve()
    output_shp_path = Path(r"E:\tobacco\tobacco_det\work_dirs\config\20250627_181417\tod_P061301_1.shp").resolve()

    run(img_path, output_shp_path)
