{"lr": 4e-05, "data_time": 7.192049741744995, "loss": 10.424835205078125, "loss_cls": 1.2211670279502869, "loss_bbox": 4.5377044677734375, "loss_obj": 4.665963411331177, "time": 9.977554440498352, "epoch": 1, "iter": 2, "memory": 17951, "step": 2}
{"lr": 0.00016, "data_time": 4.710572957992554, "loss": 10.416436433792114, "loss_cls": 1.2744528651237488, "loss_bbox": 4.489258885383606, "loss_obj": 4.652724504470825, "time": 7.188295900821686, "epoch": 2, "iter": 4, "memory": 17542, "step": 4}
{"lr": 0.00035999999999999997, "data_time": 3.926505168279012, "loss": 10.335603872934977, "loss_cls": 1.3150461713473003, "loss_bbox": 4.4015107949574785, "loss_obj": 4.619046688079834, "time": 6.448767383893331, "epoch": 3, "iter": 6, "memory": 18009, "step": 6}
{"lr": 0.0006399999999999999, "data_time": 3.5070579648017883, "loss": 10.236953854560852, "loss_cls": 1.3342726975679398, "loss_bbox": 4.296693325042725, "loss_obj": 4.605987727642059, "time": 6.045951783657074, "epoch": 4, "iter": 8, "memory": 18409, "step": 8}
{"lr": 0.001, "data_time": 3.233466958999634, "loss": 10.106788539886475, "loss_cls": 1.3241722106933593, "loss_bbox": 4.231570053100586, "loss_obj": 4.55104627609253, "time": 5.779408383369446, "epoch": 5, "iter": 10, "memory": 17920, "step": 10}
{"lr": 0.000999992785395163, "data_time": 3.0660340587298074, "loss": 9.969622135162354, "loss_cls": 1.3003965814908345, "loss_bbox": 4.1985511382420855, "loss_obj": 4.470674415429433, "time": 5.605211794376373, "epoch": 6, "iter": 12, "memory": 11414, "step": 12}
{"lr": 0.0009999350698714224, "data_time": 2.951570510864258, "loss": 9.791145733424596, "loss_cls": 1.257160267659596, "loss_bbox": 4.19874906539917, "loss_obj": 4.3352364131382535, "time": 5.501193250928607, "epoch": 7, "iter": 14, "memory": 11711, "step": 14}
{"lr": 0.0009998196458368515, "data_time": 2.8647985458374023, "loss": 9.612477600574493, "loss_cls": 1.2142791152000427, "loss_bbox": 4.197790026664734, "loss_obj": 4.200408473610878, "time": 5.397861272096634, "epoch": 8, "iter": 16, "memory": 11410, "step": 16}
{"lr": 0.0009996465273164183, "data_time": 2.789831187989977, "loss": 9.4400741259257, "loss_cls": 1.1800933447149065, "loss_bbox": 4.191312842898899, "loss_obj": 4.0686679813596935, "time": 5.282404661178589, "epoch": 9, "iter": 18, "memory": 11362, "step": 18}
{"lr": 0.000999415735345444, "data_time": 2.738728642463684, "loss": 9.248448991775513, "loss_cls": 1.1442814141511917, "loss_bbox": 4.183681607246399, "loss_obj": 3.920486032962799, "time": 5.223161280155182, "epoch": 10, "iter": 20, "memory": 11465, "step": 20}
{"data_time": 9.958108901977539, "time": 10.251260042190552, "step": 10}
{"lr": 0.000999127297967049, "data_time": 2.6933734633705835, "loss": 9.087738037109375, "loss_cls": 1.1120089482177387, "loss_bbox": 4.175340782512318, "loss_obj": 3.8003883361816406, "time": 5.162442391568964, "epoch": 11, "iter": 22, "memory": 13141, "step": 22}
{"lr": 0.000998781250228743, "data_time": 2.6559045612812042, "loss": 8.936084389686584, "loss_cls": 1.0845649639765422, "loss_bbox": 4.163652290900548, "loss_obj": 3.6878671844800315, "time": 5.091633876164754, "epoch": 12, "iter": 24, "memory": 12724, "step": 24}
{"lr": 0.0009983776341781674, "data_time": 2.625507042958186, "loss": 8.780436204029964, "loss_cls": 1.0603852294958556, "loss_bbox": 4.1482908358940715, "loss_obj": 3.571760205122141, "time": 5.045885654596182, "epoch": 13, "iter": 26, "memory": 12834, "step": 26}
{"lr": 0.0009979164988579866, "data_time": 2.593494951725006, "loss": 8.648445980889457, "loss_cls": 1.0382777048008782, "loss_bbox": 4.141792552811759, "loss_obj": 3.468375767980303, "time": 4.9975323506764004, "epoch": 14, "iter": 28, "memory": 12710, "step": 28}
{"lr": 0.0009973979002999285, "data_time": 2.571010629336039, "loss": 8.534081888198852, "loss_cls": 1.020000664393107, "loss_bbox": 4.130262533823649, "loss_obj": 3.383818745613098, "time": 4.968536520004273, "epoch": 15, "iter": 30, "memory": 13124, "step": 30}
{"lr": 0.0009968219015179768, "data_time": 2.553543671965599, "loss": 8.417891830205917, "loss_cls": 1.0013575982302427, "loss_bbox": 4.121276214718819, "loss_obj": 3.2952580861747265, "time": 6.0115058571100235, "epoch": 16, "iter": 32, "memory": 27402, "step": 32}
{"lr": 0.0009961885725007127, "data_time": 2.5342731686199413, "loss": 8.318685475517722, "loss_cls": 0.9861651343457839, "loss_bbox": 4.117472522399005, "loss_obj": 3.215047874871422, "time": 6.428213336888482, "epoch": 17, "iter": 34, "memory": 27608, "step": 34}
{"lr": 0.0009954979902028122, "data_time": 2.527742154068417, "loss": 8.224277959929573, "loss_cls": 0.9714265134599473, "loss_bbox": 4.107459485530853, "loss_obj": 3.145392027166155, "time": 6.676902267667982, "epoch": 18, "iter": 36, "memory": 27234, "step": 36}
{"lr": 0.0009947502385356957, "data_time": 2.5187481453544214, "loss": 8.133914320092453, "loss_cls": 0.9602415342079965, "loss_bbox": 4.094837627912822, "loss_obj": 3.0788352207133642, "time": 7.017684214993527, "epoch": 19, "iter": 38, "memory": 28459, "step": 38}
{"lr": 0.0009939454083573304, "data_time": 2.5062808811664583, "loss": 8.051640844345092, "loss_cls": 0.9495243385434151, "loss_bbox": 4.081335294246673, "loss_obj": 3.0207812637090683, "time": 7.270647644996643, "epoch": 20, "iter": 40, "memory": 27979, "step": 40}
{"coco/bbox_mAP": 0.0, "coco/bbox_mAP_50": 0.0, "coco/bbox_mAP_75": 0.0, "coco/bbox_mAP_s": 0.0, "coco/bbox_mAP_m": -1.0, "coco/bbox_mAP_l": -1.0, "data_time": 4.990030646324158, "time": 5.260658264160156, "step": 20}
{"lr": 0.0009930835974611915, "data_time": 2.4900463705971125, "loss": 7.981857822054908, "loss_cls": 0.9373049693448203, "loss_bbox": 4.082615568524315, "loss_obj": 2.961937325341361, "time": 7.374126843043736, "epoch": 21, "iter": 42, "memory": 22971, "step": 42}
{"lr": 0.0009921649105643798, "data_time": 2.477711233225736, "loss": 7.9097138751636855, "loss_cls": 0.9278093197128989, "loss_bbox": 4.073467644778165, "loss_obj": 2.9084369486028496, "time": 7.4383187890052795, "epoch": 22, "iter": 44, "memory": 23410, "step": 44}
{"lr": 0.0009911894592948966, "data_time": 2.4664336702098018, "loss": 7.844342677489571, "loss_cls": 0.9187807233437247, "loss_bbox": 4.069515653278517, "loss_obj": 2.856046331965405, "time": 7.498491131741067, "epoch": 23, "iter": 46, "memory": 23478, "step": 46}
